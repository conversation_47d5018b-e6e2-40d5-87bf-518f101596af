"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-json-stable-stringify";
exports.ids = ["vendor-chunks/fast-json-stable-stringify"];
exports.modules = {

/***/ "(rsc)/./node_modules/fast-json-stable-stringify/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/fast-json-stable-stringify/index.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = function (data, opts) {\n    if (!opts) opts = {};\n    if (typeof opts === 'function') opts = { cmp: opts };\n    var cycles = (typeof opts.cycles === 'boolean') ? opts.cycles : false;\n\n    var cmp = opts.cmp && (function (f) {\n        return function (node) {\n            return function (a, b) {\n                var aobj = { key: a, value: node[a] };\n                var bobj = { key: b, value: node[b] };\n                return f(aobj, bobj);\n            };\n        };\n    })(opts.cmp);\n\n    var seen = [];\n    return (function stringify (node) {\n        if (node && node.toJSON && typeof node.toJSON === 'function') {\n            node = node.toJSON();\n        }\n\n        if (node === undefined) return;\n        if (typeof node == 'number') return isFinite(node) ? '' + node : 'null';\n        if (typeof node !== 'object') return JSON.stringify(node);\n\n        var i, out;\n        if (Array.isArray(node)) {\n            out = '[';\n            for (i = 0; i < node.length; i++) {\n                if (i) out += ',';\n                out += stringify(node[i]) || 'null';\n            }\n            return out + ']';\n        }\n\n        if (node === null) return 'null';\n\n        if (seen.indexOf(node) !== -1) {\n            if (cycles) return JSON.stringify('__cycle__');\n            throw new TypeError('Converting circular structure to JSON');\n        }\n\n        var seenIndex = seen.push(node) - 1;\n        var keys = Object.keys(node).sort(cmp && cmp(node));\n        out = '';\n        for (i = 0; i < keys.length; i++) {\n            var key = keys[i];\n            var value = stringify(node[key]);\n\n            if (!value) continue;\n            if (out) out += ',';\n            out += JSON.stringify(key) + ':' + value;\n        }\n        seen.splice(seenIndex, 1);\n        return '{' + out + '}';\n    })(data);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/fast-json-stable-stringify/index.js\n");

/***/ })

};
;