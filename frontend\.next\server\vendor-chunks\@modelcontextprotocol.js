"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@modelcontextprotocol";
exports.ids = ["vendor-chunks/@modelcontextprotocol"];
exports.modules = {

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/auth.js":
/*!************************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/client/auth.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UnauthorizedError: () => (/* binding */ UnauthorizedError),\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   discoverOAuthMetadata: () => (/* binding */ discoverOAuthMetadata),\n/* harmony export */   discoverOAuthProtectedResourceMetadata: () => (/* binding */ discoverOAuthProtectedResourceMetadata),\n/* harmony export */   exchangeAuthorization: () => (/* binding */ exchangeAuthorization),\n/* harmony export */   extractResourceMetadataUrl: () => (/* binding */ extractResourceMetadataUrl),\n/* harmony export */   refreshAuthorization: () => (/* binding */ refreshAuthorization),\n/* harmony export */   registerClient: () => (/* binding */ registerClient),\n/* harmony export */   startAuthorization: () => (/* binding */ startAuthorization)\n/* harmony export */ });\n/* harmony import */ var pkce_challenge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pkce-challenge */ \"(rsc)/./node_modules/pkce-challenge/dist/index.node.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js\");\n/* harmony import */ var _shared_auth_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../shared/auth.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/auth.js\");\n\n\n\nclass UnauthorizedError extends Error {\n    constructor(message) {\n        super(message !== null && message !== void 0 ? message : \"Unauthorized\");\n    }\n}\n/**\n * Orchestrates the full auth flow with a server.\n *\n * This can be used as a single entry point for all authorization functionality,\n * instead of linking together the other lower-level functions in this module.\n */\nasync function auth(provider, { serverUrl, authorizationCode, scope, resourceMetadataUrl }) {\n    let authorizationServerUrl = serverUrl;\n    try {\n        const resourceMetadata = await discoverOAuthProtectedResourceMetadata(resourceMetadataUrl || serverUrl);\n        if (resourceMetadata.authorization_servers && resourceMetadata.authorization_servers.length > 0) {\n            authorizationServerUrl = resourceMetadata.authorization_servers[0];\n        }\n    }\n    catch (error) {\n        console.warn(\"Could not load OAuth Protected Resource metadata, falling back to /.well-known/oauth-authorization-server\", error);\n    }\n    const metadata = await discoverOAuthMetadata(authorizationServerUrl);\n    // Handle client registration if needed\n    let clientInformation = await Promise.resolve(provider.clientInformation());\n    if (!clientInformation) {\n        if (authorizationCode !== undefined) {\n            throw new Error(\"Existing OAuth client information is required when exchanging an authorization code\");\n        }\n        if (!provider.saveClientInformation) {\n            throw new Error(\"OAuth client information must be saveable for dynamic registration\");\n        }\n        const fullInformation = await registerClient(authorizationServerUrl, {\n            metadata,\n            clientMetadata: provider.clientMetadata,\n        });\n        await provider.saveClientInformation(fullInformation);\n        clientInformation = fullInformation;\n    }\n    // Exchange authorization code for tokens\n    if (authorizationCode !== undefined) {\n        const codeVerifier = await provider.codeVerifier();\n        const tokens = await exchangeAuthorization(authorizationServerUrl, {\n            metadata,\n            clientInformation,\n            authorizationCode,\n            codeVerifier,\n            redirectUri: provider.redirectUrl,\n        });\n        await provider.saveTokens(tokens);\n        return \"AUTHORIZED\";\n    }\n    const tokens = await provider.tokens();\n    // Handle token refresh or new authorization\n    if (tokens === null || tokens === void 0 ? void 0 : tokens.refresh_token) {\n        try {\n            // Attempt to refresh the token\n            const newTokens = await refreshAuthorization(authorizationServerUrl, {\n                metadata,\n                clientInformation,\n                refreshToken: tokens.refresh_token,\n            });\n            await provider.saveTokens(newTokens);\n            return \"AUTHORIZED\";\n        }\n        catch (error) {\n            console.error(\"Could not refresh OAuth tokens:\", error);\n        }\n    }\n    const state = provider.state ? await provider.state() : undefined;\n    // Start new authorization flow\n    const { authorizationUrl, codeVerifier } = await startAuthorization(authorizationServerUrl, {\n        metadata,\n        clientInformation,\n        state,\n        redirectUrl: provider.redirectUrl,\n        scope: scope || provider.clientMetadata.scope,\n    });\n    await provider.saveCodeVerifier(codeVerifier);\n    await provider.redirectToAuthorization(authorizationUrl);\n    return \"REDIRECT\";\n}\n/**\n * Extract resource_metadata from response header.\n */\nfunction extractResourceMetadataUrl(res) {\n    const authenticateHeader = res.headers.get(\"WWW-Authenticate\");\n    if (!authenticateHeader) {\n        return undefined;\n    }\n    const [type, scheme] = authenticateHeader.split(' ');\n    if (type.toLowerCase() !== 'bearer' || !scheme) {\n        console.log(\"Invalid WWW-Authenticate header format, expected 'Bearer'\");\n        return undefined;\n    }\n    const regex = /resource_metadata=\"([^\"]*)\"/;\n    const match = regex.exec(authenticateHeader);\n    if (!match) {\n        return undefined;\n    }\n    try {\n        return new URL(match[1]);\n    }\n    catch (_a) {\n        console.log(\"Invalid resource metadata url: \", match[1]);\n        return undefined;\n    }\n}\n/**\n * Looks up RFC 9728 OAuth 2.0 Protected Resource Metadata.\n *\n * If the server returns a 404 for the well-known endpoint, this function will\n * return `undefined`. Any other errors will be thrown as exceptions.\n */\nasync function discoverOAuthProtectedResourceMetadata(serverUrl, opts) {\n    var _a;\n    let url;\n    if (opts === null || opts === void 0 ? void 0 : opts.resourceMetadataUrl) {\n        url = new URL(opts === null || opts === void 0 ? void 0 : opts.resourceMetadataUrl);\n    }\n    else {\n        url = new URL(\"/.well-known/oauth-protected-resource\", serverUrl);\n    }\n    let response;\n    try {\n        response = await fetch(url, {\n            headers: {\n                \"MCP-Protocol-Version\": (_a = opts === null || opts === void 0 ? void 0 : opts.protocolVersion) !== null && _a !== void 0 ? _a : _types_js__WEBPACK_IMPORTED_MODULE_1__.LATEST_PROTOCOL_VERSION\n            }\n        });\n    }\n    catch (error) {\n        // CORS errors come back as TypeError\n        if (error instanceof TypeError) {\n            response = await fetch(url);\n        }\n        else {\n            throw error;\n        }\n    }\n    if (response.status === 404) {\n        throw new Error(`Resource server does not implement OAuth 2.0 Protected Resource Metadata.`);\n    }\n    if (!response.ok) {\n        throw new Error(`HTTP ${response.status} trying to load well-known OAuth protected resource metadata.`);\n    }\n    return _shared_auth_js__WEBPACK_IMPORTED_MODULE_2__.OAuthProtectedResourceMetadataSchema.parse(await response.json());\n}\n/**\n * Looks up RFC 8414 OAuth 2.0 Authorization Server Metadata.\n *\n * If the server returns a 404 for the well-known endpoint, this function will\n * return `undefined`. Any other errors will be thrown as exceptions.\n */\nasync function discoverOAuthMetadata(authorizationServerUrl, opts) {\n    var _a;\n    const url = new URL(\"/.well-known/oauth-authorization-server\", authorizationServerUrl);\n    let response;\n    try {\n        response = await fetch(url, {\n            headers: {\n                \"MCP-Protocol-Version\": (_a = opts === null || opts === void 0 ? void 0 : opts.protocolVersion) !== null && _a !== void 0 ? _a : _types_js__WEBPACK_IMPORTED_MODULE_1__.LATEST_PROTOCOL_VERSION\n            }\n        });\n    }\n    catch (error) {\n        // CORS errors come back as TypeError\n        if (error instanceof TypeError) {\n            response = await fetch(url);\n        }\n        else {\n            throw error;\n        }\n    }\n    if (response.status === 404) {\n        return undefined;\n    }\n    if (!response.ok) {\n        throw new Error(`HTTP ${response.status} trying to load well-known OAuth metadata`);\n    }\n    return _shared_auth_js__WEBPACK_IMPORTED_MODULE_2__.OAuthMetadataSchema.parse(await response.json());\n}\n/**\n * Begins the authorization flow with the given server, by generating a PKCE challenge and constructing the authorization URL.\n */\nasync function startAuthorization(authorizationServerUrl, { metadata, clientInformation, redirectUrl, scope, state, }) {\n    const responseType = \"code\";\n    const codeChallengeMethod = \"S256\";\n    let authorizationUrl;\n    if (metadata) {\n        authorizationUrl = new URL(metadata.authorization_endpoint);\n        if (!metadata.response_types_supported.includes(responseType)) {\n            throw new Error(`Incompatible auth server: does not support response type ${responseType}`);\n        }\n        if (!metadata.code_challenge_methods_supported ||\n            !metadata.code_challenge_methods_supported.includes(codeChallengeMethod)) {\n            throw new Error(`Incompatible auth server: does not support code challenge method ${codeChallengeMethod}`);\n        }\n    }\n    else {\n        authorizationUrl = new URL(\"/authorize\", authorizationServerUrl);\n    }\n    // Generate PKCE challenge\n    const challenge = await (0,pkce_challenge__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n    const codeVerifier = challenge.code_verifier;\n    const codeChallenge = challenge.code_challenge;\n    authorizationUrl.searchParams.set(\"response_type\", responseType);\n    authorizationUrl.searchParams.set(\"client_id\", clientInformation.client_id);\n    authorizationUrl.searchParams.set(\"code_challenge\", codeChallenge);\n    authorizationUrl.searchParams.set(\"code_challenge_method\", codeChallengeMethod);\n    authorizationUrl.searchParams.set(\"redirect_uri\", String(redirectUrl));\n    if (state) {\n        authorizationUrl.searchParams.set(\"state\", state);\n    }\n    if (scope) {\n        authorizationUrl.searchParams.set(\"scope\", scope);\n    }\n    return { authorizationUrl, codeVerifier };\n}\n/**\n * Exchanges an authorization code for an access token with the given server.\n */\nasync function exchangeAuthorization(authorizationServerUrl, { metadata, clientInformation, authorizationCode, codeVerifier, redirectUri, }) {\n    const grantType = \"authorization_code\";\n    let tokenUrl;\n    if (metadata) {\n        tokenUrl = new URL(metadata.token_endpoint);\n        if (metadata.grant_types_supported &&\n            !metadata.grant_types_supported.includes(grantType)) {\n            throw new Error(`Incompatible auth server: does not support grant type ${grantType}`);\n        }\n    }\n    else {\n        tokenUrl = new URL(\"/token\", authorizationServerUrl);\n    }\n    // Exchange code for tokens\n    const params = new URLSearchParams({\n        grant_type: grantType,\n        client_id: clientInformation.client_id,\n        code: authorizationCode,\n        code_verifier: codeVerifier,\n        redirect_uri: String(redirectUri),\n    });\n    if (clientInformation.client_secret) {\n        params.set(\"client_secret\", clientInformation.client_secret);\n    }\n    const response = await fetch(tokenUrl, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n        },\n        body: params,\n    });\n    if (!response.ok) {\n        throw new Error(`Token exchange failed: HTTP ${response.status}`);\n    }\n    return _shared_auth_js__WEBPACK_IMPORTED_MODULE_2__.OAuthTokensSchema.parse(await response.json());\n}\n/**\n * Exchange a refresh token for an updated access token.\n */\nasync function refreshAuthorization(authorizationServerUrl, { metadata, clientInformation, refreshToken, }) {\n    const grantType = \"refresh_token\";\n    let tokenUrl;\n    if (metadata) {\n        tokenUrl = new URL(metadata.token_endpoint);\n        if (metadata.grant_types_supported &&\n            !metadata.grant_types_supported.includes(grantType)) {\n            throw new Error(`Incompatible auth server: does not support grant type ${grantType}`);\n        }\n    }\n    else {\n        tokenUrl = new URL(\"/token\", authorizationServerUrl);\n    }\n    // Exchange refresh token\n    const params = new URLSearchParams({\n        grant_type: grantType,\n        client_id: clientInformation.client_id,\n        refresh_token: refreshToken,\n    });\n    if (clientInformation.client_secret) {\n        params.set(\"client_secret\", clientInformation.client_secret);\n    }\n    const response = await fetch(tokenUrl, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/x-www-form-urlencoded\",\n        },\n        body: params,\n    });\n    if (!response.ok) {\n        throw new Error(`Token refresh failed: HTTP ${response.status}`);\n    }\n    return _shared_auth_js__WEBPACK_IMPORTED_MODULE_2__.OAuthTokensSchema.parse({ refresh_token: refreshToken, ...(await response.json()) });\n}\n/**\n * Performs OAuth 2.0 Dynamic Client Registration according to RFC 7591.\n */\nasync function registerClient(authorizationServerUrl, { metadata, clientMetadata, }) {\n    let registrationUrl;\n    if (metadata) {\n        if (!metadata.registration_endpoint) {\n            throw new Error(\"Incompatible auth server: does not support dynamic client registration\");\n        }\n        registrationUrl = new URL(metadata.registration_endpoint);\n    }\n    else {\n        registrationUrl = new URL(\"/register\", authorizationServerUrl);\n    }\n    const response = await fetch(registrationUrl, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(clientMetadata),\n    });\n    if (!response.ok) {\n        throw new Error(`Dynamic client registration failed: HTTP ${response.status}`);\n    }\n    return _shared_auth_js__WEBPACK_IMPORTED_MODULE_2__.OAuthClientInformationFullSchema.parse(await response.json());\n}\n//# sourceMappingURL=auth.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/auth.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Client: () => (/* binding */ Client)\n/* harmony export */ });\n/* harmony import */ var _shared_protocol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../shared/protocol.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/protocol.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../types.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js\");\n/* harmony import */ var ajv__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ajv */ \"(rsc)/./node_modules/ajv/lib/ajv.js\");\n\n\n\n/**\n * An MCP client on top of a pluggable transport.\n *\n * The client will automatically begin the initialization flow with the server when connect() is called.\n *\n * To use with custom types, extend the base Request/Notification/Result types and pass them as type parameters:\n *\n * ```typescript\n * // Custom schemas\n * const CustomRequestSchema = RequestSchema.extend({...})\n * const CustomNotificationSchema = NotificationSchema.extend({...})\n * const CustomResultSchema = ResultSchema.extend({...})\n *\n * // Type aliases\n * type CustomRequest = z.infer<typeof CustomRequestSchema>\n * type CustomNotification = z.infer<typeof CustomNotificationSchema>\n * type CustomResult = z.infer<typeof CustomResultSchema>\n *\n * // Create typed client\n * const client = new Client<CustomRequest, CustomNotification, CustomResult>({\n *   name: \"CustomClient\",\n *   version: \"1.0.0\"\n * })\n * ```\n */\nclass Client extends _shared_protocol_js__WEBPACK_IMPORTED_MODULE_0__.Protocol {\n    /**\n     * Initializes this client with the given name and version information.\n     */\n    constructor(_clientInfo, options) {\n        var _a;\n        super(options);\n        this._clientInfo = _clientInfo;\n        this._cachedToolOutputValidators = new Map();\n        this._capabilities = (_a = options === null || options === void 0 ? void 0 : options.capabilities) !== null && _a !== void 0 ? _a : {};\n        this._ajv = new ajv__WEBPACK_IMPORTED_MODULE_2__();\n    }\n    /**\n     * Registers new capabilities. This can only be called before connecting to a transport.\n     *\n     * The new capabilities will be merged with any existing capabilities previously given (e.g., at initialization).\n     */\n    registerCapabilities(capabilities) {\n        if (this.transport) {\n            throw new Error(\"Cannot register capabilities after connecting to transport\");\n        }\n        this._capabilities = (0,_shared_protocol_js__WEBPACK_IMPORTED_MODULE_0__.mergeCapabilities)(this._capabilities, capabilities);\n    }\n    assertCapability(capability, method) {\n        var _a;\n        if (!((_a = this._serverCapabilities) === null || _a === void 0 ? void 0 : _a[capability])) {\n            throw new Error(`Server does not support ${capability} (required for ${method})`);\n        }\n    }\n    async connect(transport, options) {\n        await super.connect(transport);\n        // When transport sessionId is already set this means we are trying to reconnect.\n        // In this case we don't need to initialize again.\n        if (transport.sessionId !== undefined) {\n            return;\n        }\n        try {\n            const result = await this.request({\n                method: \"initialize\",\n                params: {\n                    protocolVersion: _types_js__WEBPACK_IMPORTED_MODULE_1__.LATEST_PROTOCOL_VERSION,\n                    capabilities: this._capabilities,\n                    clientInfo: this._clientInfo,\n                },\n            }, _types_js__WEBPACK_IMPORTED_MODULE_1__.InitializeResultSchema, options);\n            if (result === undefined) {\n                throw new Error(`Server sent invalid initialize result: ${result}`);\n            }\n            if (!_types_js__WEBPACK_IMPORTED_MODULE_1__.SUPPORTED_PROTOCOL_VERSIONS.includes(result.protocolVersion)) {\n                throw new Error(`Server's protocol version is not supported: ${result.protocolVersion}`);\n            }\n            this._serverCapabilities = result.capabilities;\n            this._serverVersion = result.serverInfo;\n            this._instructions = result.instructions;\n            await this.notification({\n                method: \"notifications/initialized\",\n            });\n        }\n        catch (error) {\n            // Disconnect if initialization fails.\n            void this.close();\n            throw error;\n        }\n    }\n    /**\n     * After initialization has completed, this will be populated with the server's reported capabilities.\n     */\n    getServerCapabilities() {\n        return this._serverCapabilities;\n    }\n    /**\n     * After initialization has completed, this will be populated with information about the server's name and version.\n     */\n    getServerVersion() {\n        return this._serverVersion;\n    }\n    /**\n     * After initialization has completed, this may be populated with information about the server's instructions.\n     */\n    getInstructions() {\n        return this._instructions;\n    }\n    assertCapabilityForMethod(method) {\n        var _a, _b, _c, _d, _e;\n        switch (method) {\n            case \"logging/setLevel\":\n                if (!((_a = this._serverCapabilities) === null || _a === void 0 ? void 0 : _a.logging)) {\n                    throw new Error(`Server does not support logging (required for ${method})`);\n                }\n                break;\n            case \"prompts/get\":\n            case \"prompts/list\":\n                if (!((_b = this._serverCapabilities) === null || _b === void 0 ? void 0 : _b.prompts)) {\n                    throw new Error(`Server does not support prompts (required for ${method})`);\n                }\n                break;\n            case \"resources/list\":\n            case \"resources/templates/list\":\n            case \"resources/read\":\n            case \"resources/subscribe\":\n            case \"resources/unsubscribe\":\n                if (!((_c = this._serverCapabilities) === null || _c === void 0 ? void 0 : _c.resources)) {\n                    throw new Error(`Server does not support resources (required for ${method})`);\n                }\n                if (method === \"resources/subscribe\" &&\n                    !this._serverCapabilities.resources.subscribe) {\n                    throw new Error(`Server does not support resource subscriptions (required for ${method})`);\n                }\n                break;\n            case \"tools/call\":\n            case \"tools/list\":\n                if (!((_d = this._serverCapabilities) === null || _d === void 0 ? void 0 : _d.tools)) {\n                    throw new Error(`Server does not support tools (required for ${method})`);\n                }\n                break;\n            case \"completion/complete\":\n                if (!((_e = this._serverCapabilities) === null || _e === void 0 ? void 0 : _e.completions)) {\n                    throw new Error(`Server does not support completions (required for ${method})`);\n                }\n                break;\n            case \"initialize\":\n                // No specific capability required for initialize\n                break;\n            case \"ping\":\n                // No specific capability required for ping\n                break;\n        }\n    }\n    assertNotificationCapability(method) {\n        var _a;\n        switch (method) {\n            case \"notifications/roots/list_changed\":\n                if (!((_a = this._capabilities.roots) === null || _a === void 0 ? void 0 : _a.listChanged)) {\n                    throw new Error(`Client does not support roots list changed notifications (required for ${method})`);\n                }\n                break;\n            case \"notifications/initialized\":\n                // No specific capability required for initialized\n                break;\n            case \"notifications/cancelled\":\n                // Cancellation notifications are always allowed\n                break;\n            case \"notifications/progress\":\n                // Progress notifications are always allowed\n                break;\n        }\n    }\n    assertRequestHandlerCapability(method) {\n        switch (method) {\n            case \"sampling/createMessage\":\n                if (!this._capabilities.sampling) {\n                    throw new Error(`Client does not support sampling capability (required for ${method})`);\n                }\n                break;\n            case \"roots/list\":\n                if (!this._capabilities.roots) {\n                    throw new Error(`Client does not support roots capability (required for ${method})`);\n                }\n                break;\n            case \"ping\":\n                // No specific capability required for ping\n                break;\n        }\n    }\n    async ping(options) {\n        return this.request({ method: \"ping\" }, _types_js__WEBPACK_IMPORTED_MODULE_1__.EmptyResultSchema, options);\n    }\n    async complete(params, options) {\n        return this.request({ method: \"completion/complete\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.CompleteResultSchema, options);\n    }\n    async setLoggingLevel(level, options) {\n        return this.request({ method: \"logging/setLevel\", params: { level } }, _types_js__WEBPACK_IMPORTED_MODULE_1__.EmptyResultSchema, options);\n    }\n    async getPrompt(params, options) {\n        return this.request({ method: \"prompts/get\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.GetPromptResultSchema, options);\n    }\n    async listPrompts(params, options) {\n        return this.request({ method: \"prompts/list\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.ListPromptsResultSchema, options);\n    }\n    async listResources(params, options) {\n        return this.request({ method: \"resources/list\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.ListResourcesResultSchema, options);\n    }\n    async listResourceTemplates(params, options) {\n        return this.request({ method: \"resources/templates/list\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.ListResourceTemplatesResultSchema, options);\n    }\n    async readResource(params, options) {\n        return this.request({ method: \"resources/read\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.ReadResourceResultSchema, options);\n    }\n    async subscribeResource(params, options) {\n        return this.request({ method: \"resources/subscribe\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.EmptyResultSchema, options);\n    }\n    async unsubscribeResource(params, options) {\n        return this.request({ method: \"resources/unsubscribe\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.EmptyResultSchema, options);\n    }\n    async callTool(params, resultSchema = _types_js__WEBPACK_IMPORTED_MODULE_1__.CallToolResultSchema, options) {\n        const result = await this.request({ method: \"tools/call\", params }, resultSchema, options);\n        // Check if the tool has an outputSchema\n        const validator = this.getToolOutputValidator(params.name);\n        if (validator) {\n            // If tool has outputSchema, it MUST return structuredContent (unless it's an error)\n            if (!result.structuredContent && !result.isError) {\n                throw new _types_js__WEBPACK_IMPORTED_MODULE_1__.McpError(_types_js__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.InvalidRequest, `Tool ${params.name} has an output schema but did not return structured content`);\n            }\n            // Only validate structured content if present (not when there's an error)\n            if (result.structuredContent) {\n                try {\n                    // Validate the structured content (which is already an object) against the schema\n                    const isValid = validator(result.structuredContent);\n                    if (!isValid) {\n                        throw new _types_js__WEBPACK_IMPORTED_MODULE_1__.McpError(_types_js__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.InvalidParams, `Structured content does not match the tool's output schema: ${this._ajv.errorsText(validator.errors)}`);\n                    }\n                }\n                catch (error) {\n                    if (error instanceof _types_js__WEBPACK_IMPORTED_MODULE_1__.McpError) {\n                        throw error;\n                    }\n                    throw new _types_js__WEBPACK_IMPORTED_MODULE_1__.McpError(_types_js__WEBPACK_IMPORTED_MODULE_1__.ErrorCode.InvalidParams, `Failed to validate structured content: ${error instanceof Error ? error.message : String(error)}`);\n                }\n            }\n        }\n        return result;\n    }\n    cacheToolOutputSchemas(tools) {\n        this._cachedToolOutputValidators.clear();\n        for (const tool of tools) {\n            // If the tool has an outputSchema, create and cache the Ajv validator\n            if (tool.outputSchema) {\n                try {\n                    const validator = this._ajv.compile(tool.outputSchema);\n                    this._cachedToolOutputValidators.set(tool.name, validator);\n                }\n                catch (error) {\n                    console.warn(`Failed to compile output schema for tool ${tool.name}: ${error}`);\n                }\n            }\n        }\n    }\n    getToolOutputValidator(toolName) {\n        return this._cachedToolOutputValidators.get(toolName);\n    }\n    async listTools(params, options) {\n        const result = await this.request({ method: \"tools/list\", params }, _types_js__WEBPACK_IMPORTED_MODULE_1__.ListToolsResultSchema, options);\n        // Cache the tools and their output schemas for future validation\n        this.cacheToolOutputSchemas(result.tools);\n        return result;\n    }\n    async sendRootsListChanged() {\n        return this.notification({ method: \"notifications/roots/list_changed\" });\n    }\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/sse.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/client/sse.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SSEClientTransport: () => (/* binding */ SSEClientTransport),\n/* harmony export */   SseError: () => (/* binding */ SseError)\n/* harmony export */ });\n/* harmony import */ var eventsource__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! eventsource */ \"(rsc)/./node_modules/eventsource/dist/index.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js\");\n/* harmony import */ var _auth_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/auth.js\");\n\n\n\nclass SseError extends Error {\n    constructor(code, message, event) {\n        super(`SSE error: ${message}`);\n        this.code = code;\n        this.event = event;\n    }\n}\n/**\n * Client transport for SSE: this will connect to a server using Server-Sent Events for receiving\n * messages and make separate POST requests for sending messages.\n */\nclass SSEClientTransport {\n    constructor(url, opts) {\n        this._url = url;\n        this._resourceMetadataUrl = undefined;\n        this._eventSourceInit = opts === null || opts === void 0 ? void 0 : opts.eventSourceInit;\n        this._requestInit = opts === null || opts === void 0 ? void 0 : opts.requestInit;\n        this._authProvider = opts === null || opts === void 0 ? void 0 : opts.authProvider;\n    }\n    async _authThenStart() {\n        var _a;\n        if (!this._authProvider) {\n            throw new _auth_js__WEBPACK_IMPORTED_MODULE_1__.UnauthorizedError(\"No auth provider\");\n        }\n        let result;\n        try {\n            result = await (0,_auth_js__WEBPACK_IMPORTED_MODULE_1__.auth)(this._authProvider, { serverUrl: this._url, resourceMetadataUrl: this._resourceMetadataUrl });\n        }\n        catch (error) {\n            (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, error);\n            throw error;\n        }\n        if (result !== \"AUTHORIZED\") {\n            throw new _auth_js__WEBPACK_IMPORTED_MODULE_1__.UnauthorizedError();\n        }\n        return await this._startOrAuth();\n    }\n    async _commonHeaders() {\n        var _a;\n        const headers = { ...(_a = this._requestInit) === null || _a === void 0 ? void 0 : _a.headers };\n        if (this._authProvider) {\n            const tokens = await this._authProvider.tokens();\n            if (tokens) {\n                headers[\"Authorization\"] = `Bearer ${tokens.access_token}`;\n            }\n        }\n        return headers;\n    }\n    _startOrAuth() {\n        return new Promise((resolve, reject) => {\n            var _a;\n            this._eventSource = new eventsource__WEBPACK_IMPORTED_MODULE_2__.EventSource(this._url.href, (_a = this._eventSourceInit) !== null && _a !== void 0 ? _a : {\n                fetch: (url, init) => this._commonHeaders().then((headers) => fetch(url, {\n                    ...init,\n                    headers: {\n                        ...headers,\n                        Accept: \"text/event-stream\"\n                    }\n                })),\n            });\n            this._abortController = new AbortController();\n            this._eventSource.onerror = (event) => {\n                var _a;\n                if (event.code === 401 && this._authProvider) {\n                    this._authThenStart().then(resolve, reject);\n                    return;\n                }\n                const error = new SseError(event.code, event.message, event);\n                reject(error);\n                (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, error);\n            };\n            this._eventSource.onopen = () => {\n                // The connection is open, but we need to wait for the endpoint to be received.\n            };\n            this._eventSource.addEventListener(\"endpoint\", (event) => {\n                var _a;\n                const messageEvent = event;\n                try {\n                    this._endpoint = new URL(messageEvent.data, this._url);\n                    if (this._endpoint.origin !== this._url.origin) {\n                        throw new Error(`Endpoint origin does not match connection origin: ${this._endpoint.origin}`);\n                    }\n                }\n                catch (error) {\n                    reject(error);\n                    (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, error);\n                    void this.close();\n                    return;\n                }\n                resolve();\n            });\n            this._eventSource.onmessage = (event) => {\n                var _a, _b;\n                const messageEvent = event;\n                let message;\n                try {\n                    message = _types_js__WEBPACK_IMPORTED_MODULE_0__.JSONRPCMessageSchema.parse(JSON.parse(messageEvent.data));\n                }\n                catch (error) {\n                    (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, error);\n                    return;\n                }\n                (_b = this.onmessage) === null || _b === void 0 ? void 0 : _b.call(this, message);\n            };\n        });\n    }\n    async start() {\n        if (this._eventSource) {\n            throw new Error(\"SSEClientTransport already started! If using Client class, note that connect() calls start() automatically.\");\n        }\n        return await this._startOrAuth();\n    }\n    /**\n     * Call this method after the user has finished authorizing via their user agent and is redirected back to the MCP client application. This will exchange the authorization code for an access token, enabling the next connection attempt to successfully auth.\n     */\n    async finishAuth(authorizationCode) {\n        if (!this._authProvider) {\n            throw new _auth_js__WEBPACK_IMPORTED_MODULE_1__.UnauthorizedError(\"No auth provider\");\n        }\n        const result = await (0,_auth_js__WEBPACK_IMPORTED_MODULE_1__.auth)(this._authProvider, { serverUrl: this._url, authorizationCode, resourceMetadataUrl: this._resourceMetadataUrl });\n        if (result !== \"AUTHORIZED\") {\n            throw new _auth_js__WEBPACK_IMPORTED_MODULE_1__.UnauthorizedError(\"Failed to authorize\");\n        }\n    }\n    async close() {\n        var _a, _b, _c;\n        (_a = this._abortController) === null || _a === void 0 ? void 0 : _a.abort();\n        (_b = this._eventSource) === null || _b === void 0 ? void 0 : _b.close();\n        (_c = this.onclose) === null || _c === void 0 ? void 0 : _c.call(this);\n    }\n    async send(message) {\n        var _a, _b, _c;\n        if (!this._endpoint) {\n            throw new Error(\"Not connected\");\n        }\n        try {\n            const commonHeaders = await this._commonHeaders();\n            const headers = new Headers({ ...commonHeaders, ...(_a = this._requestInit) === null || _a === void 0 ? void 0 : _a.headers });\n            headers.set(\"content-type\", \"application/json\");\n            const init = {\n                ...this._requestInit,\n                method: \"POST\",\n                headers,\n                body: JSON.stringify(message),\n                signal: (_b = this._abortController) === null || _b === void 0 ? void 0 : _b.signal,\n            };\n            const response = await fetch(this._endpoint, init);\n            if (!response.ok) {\n                if (response.status === 401 && this._authProvider) {\n                    this._resourceMetadataUrl = (0,_auth_js__WEBPACK_IMPORTED_MODULE_1__.extractResourceMetadataUrl)(response);\n                    const result = await (0,_auth_js__WEBPACK_IMPORTED_MODULE_1__.auth)(this._authProvider, { serverUrl: this._url, resourceMetadataUrl: this._resourceMetadataUrl });\n                    if (result !== \"AUTHORIZED\") {\n                        throw new _auth_js__WEBPACK_IMPORTED_MODULE_1__.UnauthorizedError();\n                    }\n                    // Purposely _not_ awaited, so we don't call onerror twice\n                    return this.send(message);\n                }\n                const text = await response.text().catch(() => null);\n                throw new Error(`Error POSTing to endpoint (HTTP ${response.status}): ${text}`);\n            }\n        }\n        catch (error) {\n            (_c = this.onerror) === null || _c === void 0 ? void 0 : _c.call(this, error);\n            throw error;\n        }\n    }\n}\n//# sourceMappingURL=sse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/sse.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/stdio.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/client/stdio.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_INHERITED_ENV_VARS: () => (/* binding */ DEFAULT_INHERITED_ENV_VARS),\n/* harmony export */   StdioClientTransport: () => (/* binding */ StdioClientTransport),\n/* harmony export */   getDefaultEnvironment: () => (/* binding */ getDefaultEnvironment)\n/* harmony export */ });\n/* harmony import */ var cross_spawn__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cross-spawn */ \"(rsc)/./node_modules/cross-spawn/index.js\");\n/* harmony import */ var node_process__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! node:process */ \"node:process\");\n/* harmony import */ var node_stream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node:stream */ \"node:stream\");\n/* harmony import */ var _shared_stdio_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../shared/stdio.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/stdio.js\");\n\n\n\n\n/**\n * Environment variables to inherit by default, if an environment is not explicitly given.\n */\nconst DEFAULT_INHERITED_ENV_VARS = node_process__WEBPACK_IMPORTED_MODULE_1__.platform === \"win32\"\n    ? [\n        \"APPDATA\",\n        \"HOMEDRIVE\",\n        \"HOMEPATH\",\n        \"LOCALAPPDATA\",\n        \"PATH\",\n        \"PROCESSOR_ARCHITECTURE\",\n        \"SYSTEMDRIVE\",\n        \"SYSTEMROOT\",\n        \"TEMP\",\n        \"USERNAME\",\n        \"USERPROFILE\",\n    ]\n    : /* list inspired by the default env inheritance of sudo */\n        [\"HOME\", \"LOGNAME\", \"PATH\", \"SHELL\", \"TERM\", \"USER\"];\n/**\n * Returns a default environment object including only environment variables deemed safe to inherit.\n */\nfunction getDefaultEnvironment() {\n    const env = {};\n    for (const key of DEFAULT_INHERITED_ENV_VARS) {\n        const value = node_process__WEBPACK_IMPORTED_MODULE_1__.env[key];\n        if (value === undefined) {\n            continue;\n        }\n        if (value.startsWith(\"()\")) {\n            // Skip functions, which are a security risk.\n            continue;\n        }\n        env[key] = value;\n    }\n    return env;\n}\n/**\n * Client transport for stdio: this will connect to a server by spawning a process and communicating with it over stdin/stdout.\n *\n * This transport is only available in Node.js environments.\n */\nclass StdioClientTransport {\n    constructor(server) {\n        this._abortController = new AbortController();\n        this._readBuffer = new _shared_stdio_js__WEBPACK_IMPORTED_MODULE_3__.ReadBuffer();\n        this._stderrStream = null;\n        this._serverParams = server;\n        if (server.stderr === \"pipe\" || server.stderr === \"overlapped\") {\n            this._stderrStream = new node_stream__WEBPACK_IMPORTED_MODULE_2__.PassThrough();\n        }\n    }\n    /**\n     * Starts the server process and prepares to communicate with it.\n     */\n    async start() {\n        if (this._process) {\n            throw new Error(\"StdioClientTransport already started! If using Client class, note that connect() calls start() automatically.\");\n        }\n        return new Promise((resolve, reject) => {\n            var _a, _b, _c, _d, _e, _f;\n            this._process = cross_spawn__WEBPACK_IMPORTED_MODULE_0__(this._serverParams.command, (_a = this._serverParams.args) !== null && _a !== void 0 ? _a : [], {\n                env: (_b = this._serverParams.env) !== null && _b !== void 0 ? _b : getDefaultEnvironment(),\n                stdio: [\"pipe\", \"pipe\", (_c = this._serverParams.stderr) !== null && _c !== void 0 ? _c : \"inherit\"],\n                shell: false,\n                signal: this._abortController.signal,\n                windowsHide: node_process__WEBPACK_IMPORTED_MODULE_1__.platform === \"win32\" && isElectron(),\n                cwd: this._serverParams.cwd,\n            });\n            this._process.on(\"error\", (error) => {\n                var _a, _b;\n                if (error.name === \"AbortError\") {\n                    // Expected when close() is called.\n                    (_a = this.onclose) === null || _a === void 0 ? void 0 : _a.call(this);\n                    return;\n                }\n                reject(error);\n                (_b = this.onerror) === null || _b === void 0 ? void 0 : _b.call(this, error);\n            });\n            this._process.on(\"spawn\", () => {\n                resolve();\n            });\n            this._process.on(\"close\", (_code) => {\n                var _a;\n                this._process = undefined;\n                (_a = this.onclose) === null || _a === void 0 ? void 0 : _a.call(this);\n            });\n            (_d = this._process.stdin) === null || _d === void 0 ? void 0 : _d.on(\"error\", (error) => {\n                var _a;\n                (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, error);\n            });\n            (_e = this._process.stdout) === null || _e === void 0 ? void 0 : _e.on(\"data\", (chunk) => {\n                this._readBuffer.append(chunk);\n                this.processReadBuffer();\n            });\n            (_f = this._process.stdout) === null || _f === void 0 ? void 0 : _f.on(\"error\", (error) => {\n                var _a;\n                (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, error);\n            });\n            if (this._stderrStream && this._process.stderr) {\n                this._process.stderr.pipe(this._stderrStream);\n            }\n        });\n    }\n    /**\n     * The stderr stream of the child process, if `StdioServerParameters.stderr` was set to \"pipe\" or \"overlapped\".\n     *\n     * If stderr piping was requested, a PassThrough stream is returned _immediately_, allowing callers to\n     * attach listeners before the start method is invoked. This prevents loss of any early\n     * error output emitted by the child process.\n     */\n    get stderr() {\n        var _a, _b;\n        if (this._stderrStream) {\n            return this._stderrStream;\n        }\n        return (_b = (_a = this._process) === null || _a === void 0 ? void 0 : _a.stderr) !== null && _b !== void 0 ? _b : null;\n    }\n    processReadBuffer() {\n        var _a, _b;\n        while (true) {\n            try {\n                const message = this._readBuffer.readMessage();\n                if (message === null) {\n                    break;\n                }\n                (_a = this.onmessage) === null || _a === void 0 ? void 0 : _a.call(this, message);\n            }\n            catch (error) {\n                (_b = this.onerror) === null || _b === void 0 ? void 0 : _b.call(this, error);\n            }\n        }\n    }\n    async close() {\n        this._abortController.abort();\n        this._process = undefined;\n        this._readBuffer.clear();\n    }\n    send(message) {\n        return new Promise((resolve) => {\n            var _a;\n            if (!((_a = this._process) === null || _a === void 0 ? void 0 : _a.stdin)) {\n                throw new Error(\"Not connected\");\n            }\n            const json = (0,_shared_stdio_js__WEBPACK_IMPORTED_MODULE_3__.serializeMessage)(message);\n            if (this._process.stdin.write(json)) {\n                resolve();\n            }\n            else {\n                this._process.stdin.once(\"drain\", resolve);\n            }\n        });\n    }\n}\nfunction isElectron() {\n    return \"type\" in node_process__WEBPACK_IMPORTED_MODULE_1__;\n}\n//# sourceMappingURL=stdio.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/stdio.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/streamableHttp.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/client/streamableHttp.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StreamableHTTPClientTransport: () => (/* binding */ StreamableHTTPClientTransport),\n/* harmony export */   StreamableHTTPError: () => (/* binding */ StreamableHTTPError)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js\");\n/* harmony import */ var _auth_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/auth.js\");\n/* harmony import */ var eventsource_parser_stream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! eventsource-parser/stream */ \"(rsc)/./node_modules/eventsource-parser/dist/stream.js\");\n\n\n\n// Default reconnection options for StreamableHTTP connections\nconst DEFAULT_STREAMABLE_HTTP_RECONNECTION_OPTIONS = {\n    initialReconnectionDelay: 1000,\n    maxReconnectionDelay: 30000,\n    reconnectionDelayGrowFactor: 1.5,\n    maxRetries: 2,\n};\nclass StreamableHTTPError extends Error {\n    constructor(code, message) {\n        super(`Streamable HTTP error: ${message}`);\n        this.code = code;\n    }\n}\n/**\n * Client transport for Streamable HTTP: this implements the MCP Streamable HTTP transport specification.\n * It will connect to a server using HTTP POST for sending messages and HTTP GET with Server-Sent Events\n * for receiving messages.\n */\nclass StreamableHTTPClientTransport {\n    constructor(url, opts) {\n        var _a;\n        this._url = url;\n        this._resourceMetadataUrl = undefined;\n        this._requestInit = opts === null || opts === void 0 ? void 0 : opts.requestInit;\n        this._authProvider = opts === null || opts === void 0 ? void 0 : opts.authProvider;\n        this._sessionId = opts === null || opts === void 0 ? void 0 : opts.sessionId;\n        this._reconnectionOptions = (_a = opts === null || opts === void 0 ? void 0 : opts.reconnectionOptions) !== null && _a !== void 0 ? _a : DEFAULT_STREAMABLE_HTTP_RECONNECTION_OPTIONS;\n    }\n    async _authThenStart() {\n        var _a;\n        if (!this._authProvider) {\n            throw new _auth_js__WEBPACK_IMPORTED_MODULE_1__.UnauthorizedError(\"No auth provider\");\n        }\n        let result;\n        try {\n            result = await (0,_auth_js__WEBPACK_IMPORTED_MODULE_1__.auth)(this._authProvider, { serverUrl: this._url, resourceMetadataUrl: this._resourceMetadataUrl });\n        }\n        catch (error) {\n            (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, error);\n            throw error;\n        }\n        if (result !== \"AUTHORIZED\") {\n            throw new _auth_js__WEBPACK_IMPORTED_MODULE_1__.UnauthorizedError();\n        }\n        return await this._startOrAuthSse({ resumptionToken: undefined });\n    }\n    async _commonHeaders() {\n        var _a;\n        const headers = {};\n        if (this._authProvider) {\n            const tokens = await this._authProvider.tokens();\n            if (tokens) {\n                headers[\"Authorization\"] = `Bearer ${tokens.access_token}`;\n            }\n        }\n        if (this._sessionId) {\n            headers[\"mcp-session-id\"] = this._sessionId;\n        }\n        return new Headers({ ...headers, ...(_a = this._requestInit) === null || _a === void 0 ? void 0 : _a.headers });\n    }\n    async _startOrAuthSse(options) {\n        var _a, _b;\n        const { resumptionToken } = options;\n        try {\n            // Try to open an initial SSE stream with GET to listen for server messages\n            // This is optional according to the spec - server may not support it\n            const headers = await this._commonHeaders();\n            headers.set(\"Accept\", \"text/event-stream\");\n            // Include Last-Event-ID header for resumable streams if provided\n            if (resumptionToken) {\n                headers.set(\"last-event-id\", resumptionToken);\n            }\n            const response = await fetch(this._url, {\n                method: \"GET\",\n                headers,\n                signal: (_a = this._abortController) === null || _a === void 0 ? void 0 : _a.signal,\n            });\n            if (!response.ok) {\n                if (response.status === 401 && this._authProvider) {\n                    // Need to authenticate\n                    return await this._authThenStart();\n                }\n                // 405 indicates that the server does not offer an SSE stream at GET endpoint\n                // This is an expected case that should not trigger an error\n                if (response.status === 405) {\n                    return;\n                }\n                throw new StreamableHTTPError(response.status, `Failed to open SSE stream: ${response.statusText}`);\n            }\n            this._handleSseStream(response.body, options);\n        }\n        catch (error) {\n            (_b = this.onerror) === null || _b === void 0 ? void 0 : _b.call(this, error);\n            throw error;\n        }\n    }\n    /**\n     * Calculates the next reconnection delay using  backoff algorithm\n     *\n     * @param attempt Current reconnection attempt count for the specific stream\n     * @returns Time to wait in milliseconds before next reconnection attempt\n     */\n    _getNextReconnectionDelay(attempt) {\n        // Access default values directly, ensuring they're never undefined\n        const initialDelay = this._reconnectionOptions.initialReconnectionDelay;\n        const growFactor = this._reconnectionOptions.reconnectionDelayGrowFactor;\n        const maxDelay = this._reconnectionOptions.maxReconnectionDelay;\n        // Cap at maximum delay\n        return Math.min(initialDelay * Math.pow(growFactor, attempt), maxDelay);\n    }\n    /**\n     * Schedule a reconnection attempt with exponential backoff\n     *\n     * @param lastEventId The ID of the last received event for resumability\n     * @param attemptCount Current reconnection attempt count for this specific stream\n     */\n    _scheduleReconnection(options, attemptCount = 0) {\n        var _a;\n        // Use provided options or default options\n        const maxRetries = this._reconnectionOptions.maxRetries;\n        // Check if we've exceeded maximum retry attempts\n        if (maxRetries > 0 && attemptCount >= maxRetries) {\n            (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, new Error(`Maximum reconnection attempts (${maxRetries}) exceeded.`));\n            return;\n        }\n        // Calculate next delay based on current attempt count\n        const delay = this._getNextReconnectionDelay(attemptCount);\n        // Schedule the reconnection\n        setTimeout(() => {\n            // Use the last event ID to resume where we left off\n            this._startOrAuthSse(options).catch(error => {\n                var _a;\n                (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, new Error(`Failed to reconnect SSE stream: ${error instanceof Error ? error.message : String(error)}`));\n                // Schedule another attempt if this one failed, incrementing the attempt counter\n                this._scheduleReconnection(options, attemptCount + 1);\n            });\n        }, delay);\n    }\n    _handleSseStream(stream, options) {\n        if (!stream) {\n            return;\n        }\n        const { onresumptiontoken, replayMessageId } = options;\n        let lastEventId;\n        const processStream = async () => {\n            var _a, _b, _c, _d;\n            // this is the closest we can get to trying to catch network errors\n            // if something happens reader will throw\n            try {\n                // Create a pipeline: binary stream -> text decoder -> SSE parser\n                const reader = stream\n                    .pipeThrough(new TextDecoderStream())\n                    .pipeThrough(new eventsource_parser_stream__WEBPACK_IMPORTED_MODULE_2__.EventSourceParserStream())\n                    .getReader();\n                while (true) {\n                    const { value: event, done } = await reader.read();\n                    if (done) {\n                        break;\n                    }\n                    // Update last event ID if provided\n                    if (event.id) {\n                        lastEventId = event.id;\n                        onresumptiontoken === null || onresumptiontoken === void 0 ? void 0 : onresumptiontoken(event.id);\n                    }\n                    if (!event.event || event.event === \"message\") {\n                        try {\n                            const message = _types_js__WEBPACK_IMPORTED_MODULE_0__.JSONRPCMessageSchema.parse(JSON.parse(event.data));\n                            if (replayMessageId !== undefined && (0,_types_js__WEBPACK_IMPORTED_MODULE_0__.isJSONRPCResponse)(message)) {\n                                message.id = replayMessageId;\n                            }\n                            (_a = this.onmessage) === null || _a === void 0 ? void 0 : _a.call(this, message);\n                        }\n                        catch (error) {\n                            (_b = this.onerror) === null || _b === void 0 ? void 0 : _b.call(this, error);\n                        }\n                    }\n                }\n            }\n            catch (error) {\n                // Handle stream errors - likely a network disconnect\n                (_c = this.onerror) === null || _c === void 0 ? void 0 : _c.call(this, new Error(`SSE stream disconnected: ${error}`));\n                // Attempt to reconnect if the stream disconnects unexpectedly and we aren't closing\n                if (this._abortController && !this._abortController.signal.aborted) {\n                    // Use the exponential backoff reconnection strategy\n                    if (lastEventId !== undefined) {\n                        try {\n                            this._scheduleReconnection({\n                                resumptionToken: lastEventId,\n                                onresumptiontoken,\n                                replayMessageId\n                            }, 0);\n                        }\n                        catch (error) {\n                            (_d = this.onerror) === null || _d === void 0 ? void 0 : _d.call(this, new Error(`Failed to reconnect: ${error instanceof Error ? error.message : String(error)}`));\n                        }\n                    }\n                }\n            }\n        };\n        processStream();\n    }\n    async start() {\n        if (this._abortController) {\n            throw new Error(\"StreamableHTTPClientTransport already started! If using Client class, note that connect() calls start() automatically.\");\n        }\n        this._abortController = new AbortController();\n    }\n    /**\n     * Call this method after the user has finished authorizing via their user agent and is redirected back to the MCP client application. This will exchange the authorization code for an access token, enabling the next connection attempt to successfully auth.\n     */\n    async finishAuth(authorizationCode) {\n        if (!this._authProvider) {\n            throw new _auth_js__WEBPACK_IMPORTED_MODULE_1__.UnauthorizedError(\"No auth provider\");\n        }\n        const result = await (0,_auth_js__WEBPACK_IMPORTED_MODULE_1__.auth)(this._authProvider, { serverUrl: this._url, authorizationCode, resourceMetadataUrl: this._resourceMetadataUrl });\n        if (result !== \"AUTHORIZED\") {\n            throw new _auth_js__WEBPACK_IMPORTED_MODULE_1__.UnauthorizedError(\"Failed to authorize\");\n        }\n    }\n    async close() {\n        var _a, _b;\n        // Abort any pending requests\n        (_a = this._abortController) === null || _a === void 0 ? void 0 : _a.abort();\n        (_b = this.onclose) === null || _b === void 0 ? void 0 : _b.call(this);\n    }\n    async send(message, options) {\n        var _a, _b, _c;\n        try {\n            const { resumptionToken, onresumptiontoken } = options || {};\n            if (resumptionToken) {\n                // If we have at last event ID, we need to reconnect the SSE stream\n                this._startOrAuthSse({ resumptionToken, replayMessageId: (0,_types_js__WEBPACK_IMPORTED_MODULE_0__.isJSONRPCRequest)(message) ? message.id : undefined }).catch(err => { var _a; return (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, err); });\n                return;\n            }\n            const headers = await this._commonHeaders();\n            headers.set(\"content-type\", \"application/json\");\n            headers.set(\"accept\", \"application/json, text/event-stream\");\n            const init = {\n                ...this._requestInit,\n                method: \"POST\",\n                headers,\n                body: JSON.stringify(message),\n                signal: (_a = this._abortController) === null || _a === void 0 ? void 0 : _a.signal,\n            };\n            const response = await fetch(this._url, init);\n            // Handle session ID received during initialization\n            const sessionId = response.headers.get(\"mcp-session-id\");\n            if (sessionId) {\n                this._sessionId = sessionId;\n            }\n            if (!response.ok) {\n                if (response.status === 401 && this._authProvider) {\n                    this._resourceMetadataUrl = (0,_auth_js__WEBPACK_IMPORTED_MODULE_1__.extractResourceMetadataUrl)(response);\n                    const result = await (0,_auth_js__WEBPACK_IMPORTED_MODULE_1__.auth)(this._authProvider, { serverUrl: this._url, resourceMetadataUrl: this._resourceMetadataUrl });\n                    if (result !== \"AUTHORIZED\") {\n                        throw new _auth_js__WEBPACK_IMPORTED_MODULE_1__.UnauthorizedError();\n                    }\n                    // Purposely _not_ awaited, so we don't call onerror twice\n                    return this.send(message);\n                }\n                const text = await response.text().catch(() => null);\n                throw new Error(`Error POSTing to endpoint (HTTP ${response.status}): ${text}`);\n            }\n            // If the response is 202 Accepted, there's no body to process\n            if (response.status === 202) {\n                // if the accepted notification is initialized, we start the SSE stream\n                // if it's supported by the server\n                if ((0,_types_js__WEBPACK_IMPORTED_MODULE_0__.isInitializedNotification)(message)) {\n                    // Start without a lastEventId since this is a fresh connection\n                    this._startOrAuthSse({ resumptionToken: undefined }).catch(err => { var _a; return (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, err); });\n                }\n                return;\n            }\n            // Get original message(s) for detecting request IDs\n            const messages = Array.isArray(message) ? message : [message];\n            const hasRequests = messages.filter(msg => \"method\" in msg && \"id\" in msg && msg.id !== undefined).length > 0;\n            // Check the response type\n            const contentType = response.headers.get(\"content-type\");\n            if (hasRequests) {\n                if (contentType === null || contentType === void 0 ? void 0 : contentType.includes(\"text/event-stream\")) {\n                    // Handle SSE stream responses for requests\n                    // We use the same handler as standalone streams, which now supports\n                    // reconnection with the last event ID\n                    this._handleSseStream(response.body, { onresumptiontoken });\n                }\n                else if (contentType === null || contentType === void 0 ? void 0 : contentType.includes(\"application/json\")) {\n                    // For non-streaming servers, we might get direct JSON responses\n                    const data = await response.json();\n                    const responseMessages = Array.isArray(data)\n                        ? data.map(msg => _types_js__WEBPACK_IMPORTED_MODULE_0__.JSONRPCMessageSchema.parse(msg))\n                        : [_types_js__WEBPACK_IMPORTED_MODULE_0__.JSONRPCMessageSchema.parse(data)];\n                    for (const msg of responseMessages) {\n                        (_b = this.onmessage) === null || _b === void 0 ? void 0 : _b.call(this, msg);\n                    }\n                }\n                else {\n                    throw new StreamableHTTPError(-1, `Unexpected content type: ${contentType}`);\n                }\n            }\n        }\n        catch (error) {\n            (_c = this.onerror) === null || _c === void 0 ? void 0 : _c.call(this, error);\n            throw error;\n        }\n    }\n    get sessionId() {\n        return this._sessionId;\n    }\n    /**\n     * Terminates the current session by sending a DELETE request to the server.\n     *\n     * Clients that no longer need a particular session\n     * (e.g., because the user is leaving the client application) SHOULD send an\n     * HTTP DELETE to the MCP endpoint with the Mcp-Session-Id header to explicitly\n     * terminate the session.\n     *\n     * The server MAY respond with HTTP 405 Method Not Allowed, indicating that\n     * the server does not allow clients to terminate sessions.\n     */\n    async terminateSession() {\n        var _a, _b;\n        if (!this._sessionId) {\n            return; // No session to terminate\n        }\n        try {\n            const headers = await this._commonHeaders();\n            const init = {\n                ...this._requestInit,\n                method: \"DELETE\",\n                headers,\n                signal: (_a = this._abortController) === null || _a === void 0 ? void 0 : _a.signal,\n            };\n            const response = await fetch(this._url, init);\n            // We specifically handle 405 as a valid response according to the spec,\n            // meaning the server does not support explicit session termination\n            if (!response.ok && response.status !== 405) {\n                throw new StreamableHTTPError(response.status, `Failed to terminate session: ${response.statusText}`);\n            }\n            this._sessionId = undefined;\n        }\n        catch (error) {\n            (_b = this.onerror) === null || _b === void 0 ? void 0 : _b.call(this, error);\n            throw error;\n        }\n    }\n}\n//# sourceMappingURL=streamableHttp.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG1vZGVsY29udGV4dHByb3RvY29sL3Nkay9kaXN0L2VzbS9jbGllbnQvc3RyZWFtYWJsZUh0dHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBbUg7QUFDbkM7QUFDWjtBQUNwRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSx3Q0FBd0MsUUFBUTtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsdURBQWlCO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQiw4Q0FBSSx1QkFBdUIsc0VBQXNFO0FBQzVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQix1REFBaUI7QUFDdkM7QUFDQSw0Q0FBNEMsNEJBQTRCO0FBQ3hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFELG9CQUFvQjtBQUN6RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLHlGQUF5RjtBQUN0SDtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0Isa0JBQWtCO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkZBQTZGLG9CQUFvQjtBQUNqSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLCtIQUErSCxXQUFXO0FBQzFJO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9JQUFvSSx1REFBdUQ7QUFDM0w7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixxQ0FBcUM7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLDhFQUF1QjtBQUM1RDtBQUNBO0FBQ0EsNEJBQTRCLHFCQUFxQjtBQUNqRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QywyREFBb0I7QUFDaEUsaUVBQWlFLDREQUFpQjtBQUNsRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2SEFBNkgsTUFBTTtBQUNuSTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkI7QUFDN0I7QUFDQTtBQUNBLHFJQUFxSSx1REFBdUQ7QUFDNUw7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLHVEQUFpQjtBQUN2QztBQUNBLDZCQUE2Qiw4Q0FBSSx1QkFBdUIseUZBQXlGO0FBQ2pKO0FBQ0Esc0JBQXNCLHVEQUFpQjtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHFDQUFxQztBQUN6RDtBQUNBO0FBQ0EsdUNBQXVDLGtDQUFrQywyREFBZ0Isb0NBQW9DLGlCQUFpQixRQUFRLHFGQUFxRjtBQUMzTztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0RBQWdELG9FQUEwQjtBQUMxRSx5Q0FBeUMsOENBQUksdUJBQXVCLHNFQUFzRTtBQUMxSTtBQUNBLGtDQUFrQyx1REFBaUI7QUFDbkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1FQUFtRSxnQkFBZ0IsS0FBSyxLQUFLO0FBQzdGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isb0VBQXlCO0FBQzdDO0FBQ0EsMkNBQTJDLDRCQUE0QixpQkFBaUIsUUFBUSxxRkFBcUY7QUFDckw7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsbUJBQW1CO0FBQzlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQ0FBMEMsMkRBQW9CO0FBQzlELDJCQUEyQiwyREFBb0I7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtGQUFrRixZQUFZO0FBQzlGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0ZBQStGLG9CQUFvQjtBQUNuSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcbm9kZV9tb2R1bGVzXFxAbW9kZWxjb250ZXh0cHJvdG9jb2xcXHNka1xcZGlzdFxcZXNtXFxjbGllbnRcXHN0cmVhbWFibGVIdHRwLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGlzSW5pdGlhbGl6ZWROb3RpZmljYXRpb24sIGlzSlNPTlJQQ1JlcXVlc3QsIGlzSlNPTlJQQ1Jlc3BvbnNlLCBKU09OUlBDTWVzc2FnZVNjaGVtYSB9IGZyb20gXCIuLi90eXBlcy5qc1wiO1xuaW1wb3J0IHsgYXV0aCwgZXh0cmFjdFJlc291cmNlTWV0YWRhdGFVcmwsIFVuYXV0aG9yaXplZEVycm9yIH0gZnJvbSBcIi4vYXV0aC5qc1wiO1xuaW1wb3J0IHsgRXZlbnRTb3VyY2VQYXJzZXJTdHJlYW0gfSBmcm9tIFwiZXZlbnRzb3VyY2UtcGFyc2VyL3N0cmVhbVwiO1xuLy8gRGVmYXVsdCByZWNvbm5lY3Rpb24gb3B0aW9ucyBmb3IgU3RyZWFtYWJsZUhUVFAgY29ubmVjdGlvbnNcbmNvbnN0IERFRkFVTFRfU1RSRUFNQUJMRV9IVFRQX1JFQ09OTkVDVElPTl9PUFRJT05TID0ge1xuICAgIGluaXRpYWxSZWNvbm5lY3Rpb25EZWxheTogMTAwMCxcbiAgICBtYXhSZWNvbm5lY3Rpb25EZWxheTogMzAwMDAsXG4gICAgcmVjb25uZWN0aW9uRGVsYXlHcm93RmFjdG9yOiAxLjUsXG4gICAgbWF4UmV0cmllczogMixcbn07XG5leHBvcnQgY2xhc3MgU3RyZWFtYWJsZUhUVFBFcnJvciBleHRlbmRzIEVycm9yIHtcbiAgICBjb25zdHJ1Y3Rvcihjb2RlLCBtZXNzYWdlKSB7XG4gICAgICAgIHN1cGVyKGBTdHJlYW1hYmxlIEhUVFAgZXJyb3I6ICR7bWVzc2FnZX1gKTtcbiAgICAgICAgdGhpcy5jb2RlID0gY29kZTtcbiAgICB9XG59XG4vKipcbiAqIENsaWVudCB0cmFuc3BvcnQgZm9yIFN0cmVhbWFibGUgSFRUUDogdGhpcyBpbXBsZW1lbnRzIHRoZSBNQ1AgU3RyZWFtYWJsZSBIVFRQIHRyYW5zcG9ydCBzcGVjaWZpY2F0aW9uLlxuICogSXQgd2lsbCBjb25uZWN0IHRvIGEgc2VydmVyIHVzaW5nIEhUVFAgUE9TVCBmb3Igc2VuZGluZyBtZXNzYWdlcyBhbmQgSFRUUCBHRVQgd2l0aCBTZXJ2ZXItU2VudCBFdmVudHNcbiAqIGZvciByZWNlaXZpbmcgbWVzc2FnZXMuXG4gKi9cbmV4cG9ydCBjbGFzcyBTdHJlYW1hYmxlSFRUUENsaWVudFRyYW5zcG9ydCB7XG4gICAgY29uc3RydWN0b3IodXJsLCBvcHRzKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgdGhpcy5fdXJsID0gdXJsO1xuICAgICAgICB0aGlzLl9yZXNvdXJjZU1ldGFkYXRhVXJsID0gdW5kZWZpbmVkO1xuICAgICAgICB0aGlzLl9yZXF1ZXN0SW5pdCA9IG9wdHMgPT09IG51bGwgfHwgb3B0cyA9PT0gdm9pZCAwID8gdm9pZCAwIDogb3B0cy5yZXF1ZXN0SW5pdDtcbiAgICAgICAgdGhpcy5fYXV0aFByb3ZpZGVyID0gb3B0cyA9PT0gbnVsbCB8fCBvcHRzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRzLmF1dGhQcm92aWRlcjtcbiAgICAgICAgdGhpcy5fc2Vzc2lvbklkID0gb3B0cyA9PT0gbnVsbCB8fCBvcHRzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRzLnNlc3Npb25JZDtcbiAgICAgICAgdGhpcy5fcmVjb25uZWN0aW9uT3B0aW9ucyA9IChfYSA9IG9wdHMgPT09IG51bGwgfHwgb3B0cyA9PT0gdm9pZCAwID8gdm9pZCAwIDogb3B0cy5yZWNvbm5lY3Rpb25PcHRpb25zKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiBERUZBVUxUX1NUUkVBTUFCTEVfSFRUUF9SRUNPTk5FQ1RJT05fT1BUSU9OUztcbiAgICB9XG4gICAgYXN5bmMgX2F1dGhUaGVuU3RhcnQoKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgaWYgKCF0aGlzLl9hdXRoUHJvdmlkZXIpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBVbmF1dGhvcml6ZWRFcnJvcihcIk5vIGF1dGggcHJvdmlkZXJcIik7XG4gICAgICAgIH1cbiAgICAgICAgbGV0IHJlc3VsdDtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIHJlc3VsdCA9IGF3YWl0IGF1dGgodGhpcy5fYXV0aFByb3ZpZGVyLCB7IHNlcnZlclVybDogdGhpcy5fdXJsLCByZXNvdXJjZU1ldGFkYXRhVXJsOiB0aGlzLl9yZXNvdXJjZU1ldGFkYXRhVXJsIH0pO1xuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgKF9hID0gdGhpcy5vbmVycm9yKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbCh0aGlzLCBlcnJvcik7XG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgICAgICBpZiAocmVzdWx0ICE9PSBcIkFVVEhPUklaRURcIikge1xuICAgICAgICAgICAgdGhyb3cgbmV3IFVuYXV0aG9yaXplZEVycm9yKCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGF3YWl0IHRoaXMuX3N0YXJ0T3JBdXRoU3NlKHsgcmVzdW1wdGlvblRva2VuOiB1bmRlZmluZWQgfSk7XG4gICAgfVxuICAgIGFzeW5jIF9jb21tb25IZWFkZXJzKCkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIGNvbnN0IGhlYWRlcnMgPSB7fTtcbiAgICAgICAgaWYgKHRoaXMuX2F1dGhQcm92aWRlcikge1xuICAgICAgICAgICAgY29uc3QgdG9rZW5zID0gYXdhaXQgdGhpcy5fYXV0aFByb3ZpZGVyLnRva2VucygpO1xuICAgICAgICAgICAgaWYgKHRva2Vucykge1xuICAgICAgICAgICAgICAgIGhlYWRlcnNbXCJBdXRob3JpemF0aW9uXCJdID0gYEJlYXJlciAke3Rva2Vucy5hY2Nlc3NfdG9rZW59YDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5fc2Vzc2lvbklkKSB7XG4gICAgICAgICAgICBoZWFkZXJzW1wibWNwLXNlc3Npb24taWRcIl0gPSB0aGlzLl9zZXNzaW9uSWQ7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG5ldyBIZWFkZXJzKHsgLi4uaGVhZGVycywgLi4uKF9hID0gdGhpcy5fcmVxdWVzdEluaXQpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5oZWFkZXJzIH0pO1xuICAgIH1cbiAgICBhc3luYyBfc3RhcnRPckF1dGhTc2Uob3B0aW9ucykge1xuICAgICAgICB2YXIgX2EsIF9iO1xuICAgICAgICBjb25zdCB7IHJlc3VtcHRpb25Ub2tlbiB9ID0gb3B0aW9ucztcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIFRyeSB0byBvcGVuIGFuIGluaXRpYWwgU1NFIHN0cmVhbSB3aXRoIEdFVCB0byBsaXN0ZW4gZm9yIHNlcnZlciBtZXNzYWdlc1xuICAgICAgICAgICAgLy8gVGhpcyBpcyBvcHRpb25hbCBhY2NvcmRpbmcgdG8gdGhlIHNwZWMgLSBzZXJ2ZXIgbWF5IG5vdCBzdXBwb3J0IGl0XG4gICAgICAgICAgICBjb25zdCBoZWFkZXJzID0gYXdhaXQgdGhpcy5fY29tbW9uSGVhZGVycygpO1xuICAgICAgICAgICAgaGVhZGVycy5zZXQoXCJBY2NlcHRcIiwgXCJ0ZXh0L2V2ZW50LXN0cmVhbVwiKTtcbiAgICAgICAgICAgIC8vIEluY2x1ZGUgTGFzdC1FdmVudC1JRCBoZWFkZXIgZm9yIHJlc3VtYWJsZSBzdHJlYW1zIGlmIHByb3ZpZGVkXG4gICAgICAgICAgICBpZiAocmVzdW1wdGlvblRva2VuKSB7XG4gICAgICAgICAgICAgICAgaGVhZGVycy5zZXQoXCJsYXN0LWV2ZW50LWlkXCIsIHJlc3VtcHRpb25Ub2tlbik7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHRoaXMuX3VybCwge1xuICAgICAgICAgICAgICAgIG1ldGhvZDogXCJHRVRcIixcbiAgICAgICAgICAgICAgICBoZWFkZXJzLFxuICAgICAgICAgICAgICAgIHNpZ25hbDogKF9hID0gdGhpcy5fYWJvcnRDb250cm9sbGVyKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Euc2lnbmFsLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxICYmIHRoaXMuX2F1dGhQcm92aWRlcikge1xuICAgICAgICAgICAgICAgICAgICAvLyBOZWVkIHRvIGF1dGhlbnRpY2F0ZVxuICAgICAgICAgICAgICAgICAgICByZXR1cm4gYXdhaXQgdGhpcy5fYXV0aFRoZW5TdGFydCgpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAvLyA0MDUgaW5kaWNhdGVzIHRoYXQgdGhlIHNlcnZlciBkb2VzIG5vdCBvZmZlciBhbiBTU0Ugc3RyZWFtIGF0IEdFVCBlbmRwb2ludFxuICAgICAgICAgICAgICAgIC8vIFRoaXMgaXMgYW4gZXhwZWN0ZWQgY2FzZSB0aGF0IHNob3VsZCBub3QgdHJpZ2dlciBhbiBlcnJvclxuICAgICAgICAgICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwNSkge1xuICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRocm93IG5ldyBTdHJlYW1hYmxlSFRUUEVycm9yKHJlc3BvbnNlLnN0YXR1cywgYEZhaWxlZCB0byBvcGVuIFNTRSBzdHJlYW06ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuX2hhbmRsZVNzZVN0cmVhbShyZXNwb25zZS5ib2R5LCBvcHRpb25zKTtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIChfYiA9IHRoaXMub25lcnJvcikgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLmNhbGwodGhpcywgZXJyb3IpO1xuICAgICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2FsY3VsYXRlcyB0aGUgbmV4dCByZWNvbm5lY3Rpb24gZGVsYXkgdXNpbmcgIGJhY2tvZmYgYWxnb3JpdGhtXG4gICAgICpcbiAgICAgKiBAcGFyYW0gYXR0ZW1wdCBDdXJyZW50IHJlY29ubmVjdGlvbiBhdHRlbXB0IGNvdW50IGZvciB0aGUgc3BlY2lmaWMgc3RyZWFtXG4gICAgICogQHJldHVybnMgVGltZSB0byB3YWl0IGluIG1pbGxpc2Vjb25kcyBiZWZvcmUgbmV4dCByZWNvbm5lY3Rpb24gYXR0ZW1wdFxuICAgICAqL1xuICAgIF9nZXROZXh0UmVjb25uZWN0aW9uRGVsYXkoYXR0ZW1wdCkge1xuICAgICAgICAvLyBBY2Nlc3MgZGVmYXVsdCB2YWx1ZXMgZGlyZWN0bHksIGVuc3VyaW5nIHRoZXkncmUgbmV2ZXIgdW5kZWZpbmVkXG4gICAgICAgIGNvbnN0IGluaXRpYWxEZWxheSA9IHRoaXMuX3JlY29ubmVjdGlvbk9wdGlvbnMuaW5pdGlhbFJlY29ubmVjdGlvbkRlbGF5O1xuICAgICAgICBjb25zdCBncm93RmFjdG9yID0gdGhpcy5fcmVjb25uZWN0aW9uT3B0aW9ucy5yZWNvbm5lY3Rpb25EZWxheUdyb3dGYWN0b3I7XG4gICAgICAgIGNvbnN0IG1heERlbGF5ID0gdGhpcy5fcmVjb25uZWN0aW9uT3B0aW9ucy5tYXhSZWNvbm5lY3Rpb25EZWxheTtcbiAgICAgICAgLy8gQ2FwIGF0IG1heGltdW0gZGVsYXlcbiAgICAgICAgcmV0dXJuIE1hdGgubWluKGluaXRpYWxEZWxheSAqIE1hdGgucG93KGdyb3dGYWN0b3IsIGF0dGVtcHQpLCBtYXhEZWxheSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNjaGVkdWxlIGEgcmVjb25uZWN0aW9uIGF0dGVtcHQgd2l0aCBleHBvbmVudGlhbCBiYWNrb2ZmXG4gICAgICpcbiAgICAgKiBAcGFyYW0gbGFzdEV2ZW50SWQgVGhlIElEIG9mIHRoZSBsYXN0IHJlY2VpdmVkIGV2ZW50IGZvciByZXN1bWFiaWxpdHlcbiAgICAgKiBAcGFyYW0gYXR0ZW1wdENvdW50IEN1cnJlbnQgcmVjb25uZWN0aW9uIGF0dGVtcHQgY291bnQgZm9yIHRoaXMgc3BlY2lmaWMgc3RyZWFtXG4gICAgICovXG4gICAgX3NjaGVkdWxlUmVjb25uZWN0aW9uKG9wdGlvbnMsIGF0dGVtcHRDb3VudCA9IDApIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICAvLyBVc2UgcHJvdmlkZWQgb3B0aW9ucyBvciBkZWZhdWx0IG9wdGlvbnNcbiAgICAgICAgY29uc3QgbWF4UmV0cmllcyA9IHRoaXMuX3JlY29ubmVjdGlvbk9wdGlvbnMubWF4UmV0cmllcztcbiAgICAgICAgLy8gQ2hlY2sgaWYgd2UndmUgZXhjZWVkZWQgbWF4aW11bSByZXRyeSBhdHRlbXB0c1xuICAgICAgICBpZiAobWF4UmV0cmllcyA+IDAgJiYgYXR0ZW1wdENvdW50ID49IG1heFJldHJpZXMpIHtcbiAgICAgICAgICAgIChfYSA9IHRoaXMub25lcnJvcikgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNhbGwodGhpcywgbmV3IEVycm9yKGBNYXhpbXVtIHJlY29ubmVjdGlvbiBhdHRlbXB0cyAoJHttYXhSZXRyaWVzfSkgZXhjZWVkZWQuYCkpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIC8vIENhbGN1bGF0ZSBuZXh0IGRlbGF5IGJhc2VkIG9uIGN1cnJlbnQgYXR0ZW1wdCBjb3VudFxuICAgICAgICBjb25zdCBkZWxheSA9IHRoaXMuX2dldE5leHRSZWNvbm5lY3Rpb25EZWxheShhdHRlbXB0Q291bnQpO1xuICAgICAgICAvLyBTY2hlZHVsZSB0aGUgcmVjb25uZWN0aW9uXG4gICAgICAgIHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgLy8gVXNlIHRoZSBsYXN0IGV2ZW50IElEIHRvIHJlc3VtZSB3aGVyZSB3ZSBsZWZ0IG9mZlxuICAgICAgICAgICAgdGhpcy5fc3RhcnRPckF1dGhTc2Uob3B0aW9ucykuY2F0Y2goZXJyb3IgPT4ge1xuICAgICAgICAgICAgICAgIHZhciBfYTtcbiAgICAgICAgICAgICAgICAoX2EgPSB0aGlzLm9uZXJyb3IpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5jYWxsKHRoaXMsIG5ldyBFcnJvcihgRmFpbGVkIHRvIHJlY29ubmVjdCBTU0Ugc3RyZWFtOiAke2Vycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogU3RyaW5nKGVycm9yKX1gKSk7XG4gICAgICAgICAgICAgICAgLy8gU2NoZWR1bGUgYW5vdGhlciBhdHRlbXB0IGlmIHRoaXMgb25lIGZhaWxlZCwgaW5jcmVtZW50aW5nIHRoZSBhdHRlbXB0IGNvdW50ZXJcbiAgICAgICAgICAgICAgICB0aGlzLl9zY2hlZHVsZVJlY29ubmVjdGlvbihvcHRpb25zLCBhdHRlbXB0Q291bnQgKyAxKTtcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9LCBkZWxheSk7XG4gICAgfVxuICAgIF9oYW5kbGVTc2VTdHJlYW0oc3RyZWFtLCBvcHRpb25zKSB7XG4gICAgICAgIGlmICghc3RyZWFtKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgeyBvbnJlc3VtcHRpb250b2tlbiwgcmVwbGF5TWVzc2FnZUlkIH0gPSBvcHRpb25zO1xuICAgICAgICBsZXQgbGFzdEV2ZW50SWQ7XG4gICAgICAgIGNvbnN0IHByb2Nlc3NTdHJlYW0gPSBhc3luYyAoKSA9PiB7XG4gICAgICAgICAgICB2YXIgX2EsIF9iLCBfYywgX2Q7XG4gICAgICAgICAgICAvLyB0aGlzIGlzIHRoZSBjbG9zZXN0IHdlIGNhbiBnZXQgdG8gdHJ5aW5nIHRvIGNhdGNoIG5ldHdvcmsgZXJyb3JzXG4gICAgICAgICAgICAvLyBpZiBzb21ldGhpbmcgaGFwcGVucyByZWFkZXIgd2lsbCB0aHJvd1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAvLyBDcmVhdGUgYSBwaXBlbGluZTogYmluYXJ5IHN0cmVhbSAtPiB0ZXh0IGRlY29kZXIgLT4gU1NFIHBhcnNlclxuICAgICAgICAgICAgICAgIGNvbnN0IHJlYWRlciA9IHN0cmVhbVxuICAgICAgICAgICAgICAgICAgICAucGlwZVRocm91Z2gobmV3IFRleHREZWNvZGVyU3RyZWFtKCkpXG4gICAgICAgICAgICAgICAgICAgIC5waXBlVGhyb3VnaChuZXcgRXZlbnRTb3VyY2VQYXJzZXJTdHJlYW0oKSlcbiAgICAgICAgICAgICAgICAgICAgLmdldFJlYWRlcigpO1xuICAgICAgICAgICAgICAgIHdoaWxlICh0cnVlKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHsgdmFsdWU6IGV2ZW50LCBkb25lIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoZG9uZSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgYnJlYWs7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgLy8gVXBkYXRlIGxhc3QgZXZlbnQgSUQgaWYgcHJvdmlkZWRcbiAgICAgICAgICAgICAgICAgICAgaWYgKGV2ZW50LmlkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBsYXN0RXZlbnRJZCA9IGV2ZW50LmlkO1xuICAgICAgICAgICAgICAgICAgICAgICAgb25yZXN1bXB0aW9udG9rZW4gPT09IG51bGwgfHwgb25yZXN1bXB0aW9udG9rZW4gPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9ucmVzdW1wdGlvbnRva2VuKGV2ZW50LmlkKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBpZiAoIWV2ZW50LmV2ZW50IHx8IGV2ZW50LmV2ZW50ID09PSBcIm1lc3NhZ2VcIikge1xuICAgICAgICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBtZXNzYWdlID0gSlNPTlJQQ01lc3NhZ2VTY2hlbWEucGFyc2UoSlNPTi5wYXJzZShldmVudC5kYXRhKSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJlcGxheU1lc3NhZ2VJZCAhPT0gdW5kZWZpbmVkICYmIGlzSlNPTlJQQ1Jlc3BvbnNlKG1lc3NhZ2UpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lc3NhZ2UuaWQgPSByZXBsYXlNZXNzYWdlSWQ7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIChfYSA9IHRoaXMub25tZXNzYWdlKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbCh0aGlzLCBtZXNzYWdlKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIChfYiA9IHRoaXMub25lcnJvcikgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLmNhbGwodGhpcywgZXJyb3IpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgLy8gSGFuZGxlIHN0cmVhbSBlcnJvcnMgLSBsaWtlbHkgYSBuZXR3b3JrIGRpc2Nvbm5lY3RcbiAgICAgICAgICAgICAgICAoX2MgPSB0aGlzLm9uZXJyb3IpID09PSBudWxsIHx8IF9jID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYy5jYWxsKHRoaXMsIG5ldyBFcnJvcihgU1NFIHN0cmVhbSBkaXNjb25uZWN0ZWQ6ICR7ZXJyb3J9YCkpO1xuICAgICAgICAgICAgICAgIC8vIEF0dGVtcHQgdG8gcmVjb25uZWN0IGlmIHRoZSBzdHJlYW0gZGlzY29ubmVjdHMgdW5leHBlY3RlZGx5IGFuZCB3ZSBhcmVuJ3QgY2xvc2luZ1xuICAgICAgICAgICAgICAgIGlmICh0aGlzLl9hYm9ydENvbnRyb2xsZXIgJiYgIXRoaXMuX2Fib3J0Q29udHJvbGxlci5zaWduYWwuYWJvcnRlZCkge1xuICAgICAgICAgICAgICAgICAgICAvLyBVc2UgdGhlIGV4cG9uZW50aWFsIGJhY2tvZmYgcmVjb25uZWN0aW9uIHN0cmF0ZWd5XG4gICAgICAgICAgICAgICAgICAgIGlmIChsYXN0RXZlbnRJZCAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuX3NjaGVkdWxlUmVjb25uZWN0aW9uKHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmVzdW1wdGlvblRva2VuOiBsYXN0RXZlbnRJZCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgb25yZXN1bXB0aW9udG9rZW4sXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlcGxheU1lc3NhZ2VJZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0sIDApO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgKF9kID0gdGhpcy5vbmVycm9yKSA9PT0gbnVsbCB8fCBfZCA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2QuY2FsbCh0aGlzLCBuZXcgRXJyb3IoYEZhaWxlZCB0byByZWNvbm5lY3Q6ICR7ZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiBTdHJpbmcoZXJyb3IpfWApKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICAgICAgcHJvY2Vzc1N0cmVhbSgpO1xuICAgIH1cbiAgICBhc3luYyBzdGFydCgpIHtcbiAgICAgICAgaWYgKHRoaXMuX2Fib3J0Q29udHJvbGxlcikge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiU3RyZWFtYWJsZUhUVFBDbGllbnRUcmFuc3BvcnQgYWxyZWFkeSBzdGFydGVkISBJZiB1c2luZyBDbGllbnQgY2xhc3MsIG5vdGUgdGhhdCBjb25uZWN0KCkgY2FsbHMgc3RhcnQoKSBhdXRvbWF0aWNhbGx5LlwiKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLl9hYm9ydENvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENhbGwgdGhpcyBtZXRob2QgYWZ0ZXIgdGhlIHVzZXIgaGFzIGZpbmlzaGVkIGF1dGhvcml6aW5nIHZpYSB0aGVpciB1c2VyIGFnZW50IGFuZCBpcyByZWRpcmVjdGVkIGJhY2sgdG8gdGhlIE1DUCBjbGllbnQgYXBwbGljYXRpb24uIFRoaXMgd2lsbCBleGNoYW5nZSB0aGUgYXV0aG9yaXphdGlvbiBjb2RlIGZvciBhbiBhY2Nlc3MgdG9rZW4sIGVuYWJsaW5nIHRoZSBuZXh0IGNvbm5lY3Rpb24gYXR0ZW1wdCB0byBzdWNjZXNzZnVsbHkgYXV0aC5cbiAgICAgKi9cbiAgICBhc3luYyBmaW5pc2hBdXRoKGF1dGhvcml6YXRpb25Db2RlKSB7XG4gICAgICAgIGlmICghdGhpcy5fYXV0aFByb3ZpZGVyKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgVW5hdXRob3JpemVkRXJyb3IoXCJObyBhdXRoIHByb3ZpZGVyXCIpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IGF1dGgodGhpcy5fYXV0aFByb3ZpZGVyLCB7IHNlcnZlclVybDogdGhpcy5fdXJsLCBhdXRob3JpemF0aW9uQ29kZSwgcmVzb3VyY2VNZXRhZGF0YVVybDogdGhpcy5fcmVzb3VyY2VNZXRhZGF0YVVybCB9KTtcbiAgICAgICAgaWYgKHJlc3VsdCAhPT0gXCJBVVRIT1JJWkVEXCIpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBVbmF1dGhvcml6ZWRFcnJvcihcIkZhaWxlZCB0byBhdXRob3JpemVcIik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgYXN5bmMgY2xvc2UoKSB7XG4gICAgICAgIHZhciBfYSwgX2I7XG4gICAgICAgIC8vIEFib3J0IGFueSBwZW5kaW5nIHJlcXVlc3RzXG4gICAgICAgIChfYSA9IHRoaXMuX2Fib3J0Q29udHJvbGxlcikgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmFib3J0KCk7XG4gICAgICAgIChfYiA9IHRoaXMub25jbG9zZSkgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLmNhbGwodGhpcyk7XG4gICAgfVxuICAgIGFzeW5jIHNlbmQobWVzc2FnZSwgb3B0aW9ucykge1xuICAgICAgICB2YXIgX2EsIF9iLCBfYztcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIGNvbnN0IHsgcmVzdW1wdGlvblRva2VuLCBvbnJlc3VtcHRpb250b2tlbiB9ID0gb3B0aW9ucyB8fCB7fTtcbiAgICAgICAgICAgIGlmIChyZXN1bXB0aW9uVG9rZW4pIHtcbiAgICAgICAgICAgICAgICAvLyBJZiB3ZSBoYXZlIGF0IGxhc3QgZXZlbnQgSUQsIHdlIG5lZWQgdG8gcmVjb25uZWN0IHRoZSBTU0Ugc3RyZWFtXG4gICAgICAgICAgICAgICAgdGhpcy5fc3RhcnRPckF1dGhTc2UoeyByZXN1bXB0aW9uVG9rZW4sIHJlcGxheU1lc3NhZ2VJZDogaXNKU09OUlBDUmVxdWVzdChtZXNzYWdlKSA/IG1lc3NhZ2UuaWQgOiB1bmRlZmluZWQgfSkuY2F0Y2goZXJyID0+IHsgdmFyIF9hOyByZXR1cm4gKF9hID0gdGhpcy5vbmVycm9yKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EuY2FsbCh0aGlzLCBlcnIpOyB9KTtcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb25zdCBoZWFkZXJzID0gYXdhaXQgdGhpcy5fY29tbW9uSGVhZGVycygpO1xuICAgICAgICAgICAgaGVhZGVycy5zZXQoXCJjb250ZW50LXR5cGVcIiwgXCJhcHBsaWNhdGlvbi9qc29uXCIpO1xuICAgICAgICAgICAgaGVhZGVycy5zZXQoXCJhY2NlcHRcIiwgXCJhcHBsaWNhdGlvbi9qc29uLCB0ZXh0L2V2ZW50LXN0cmVhbVwiKTtcbiAgICAgICAgICAgIGNvbnN0IGluaXQgPSB7XG4gICAgICAgICAgICAgICAgLi4udGhpcy5fcmVxdWVzdEluaXQsXG4gICAgICAgICAgICAgICAgbWV0aG9kOiBcIlBPU1RcIixcbiAgICAgICAgICAgICAgICBoZWFkZXJzLFxuICAgICAgICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KG1lc3NhZ2UpLFxuICAgICAgICAgICAgICAgIHNpZ25hbDogKF9hID0gdGhpcy5fYWJvcnRDb250cm9sbGVyKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2Euc2lnbmFsLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2godGhpcy5fdXJsLCBpbml0KTtcbiAgICAgICAgICAgIC8vIEhhbmRsZSBzZXNzaW9uIElEIHJlY2VpdmVkIGR1cmluZyBpbml0aWFsaXphdGlvblxuICAgICAgICAgICAgY29uc3Qgc2Vzc2lvbklkID0gcmVzcG9uc2UuaGVhZGVycy5nZXQoXCJtY3Atc2Vzc2lvbi1pZFwiKTtcbiAgICAgICAgICAgIGlmIChzZXNzaW9uSWQpIHtcbiAgICAgICAgICAgICAgICB0aGlzLl9zZXNzaW9uSWQgPSBzZXNzaW9uSWQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgICAgICAgaWYgKHJlc3BvbnNlLnN0YXR1cyA9PT0gNDAxICYmIHRoaXMuX2F1dGhQcm92aWRlcikge1xuICAgICAgICAgICAgICAgICAgICB0aGlzLl9yZXNvdXJjZU1ldGFkYXRhVXJsID0gZXh0cmFjdFJlc291cmNlTWV0YWRhdGFVcmwocmVzcG9uc2UpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBhdXRoKHRoaXMuX2F1dGhQcm92aWRlciwgeyBzZXJ2ZXJVcmw6IHRoaXMuX3VybCwgcmVzb3VyY2VNZXRhZGF0YVVybDogdGhpcy5fcmVzb3VyY2VNZXRhZGF0YVVybCB9KTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHJlc3VsdCAhPT0gXCJBVVRIT1JJWkVEXCIpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBVbmF1dGhvcml6ZWRFcnJvcigpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIC8vIFB1cnBvc2VseSBfbm90XyBhd2FpdGVkLCBzbyB3ZSBkb24ndCBjYWxsIG9uZXJyb3IgdHdpY2VcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRoaXMuc2VuZChtZXNzYWdlKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY29uc3QgdGV4dCA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKS5jYXRjaCgoKSA9PiBudWxsKTtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEVycm9yIFBPU1RpbmcgdG8gZW5kcG9pbnQgKEhUVFAgJHtyZXNwb25zZS5zdGF0dXN9KTogJHt0ZXh0fWApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gSWYgdGhlIHJlc3BvbnNlIGlzIDIwMiBBY2NlcHRlZCwgdGhlcmUncyBubyBib2R5IHRvIHByb2Nlc3NcbiAgICAgICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDIwMikge1xuICAgICAgICAgICAgICAgIC8vIGlmIHRoZSBhY2NlcHRlZCBub3RpZmljYXRpb24gaXMgaW5pdGlhbGl6ZWQsIHdlIHN0YXJ0IHRoZSBTU0Ugc3RyZWFtXG4gICAgICAgICAgICAgICAgLy8gaWYgaXQncyBzdXBwb3J0ZWQgYnkgdGhlIHNlcnZlclxuICAgICAgICAgICAgICAgIGlmIChpc0luaXRpYWxpemVkTm90aWZpY2F0aW9uKG1lc3NhZ2UpKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFN0YXJ0IHdpdGhvdXQgYSBsYXN0RXZlbnRJZCBzaW5jZSB0aGlzIGlzIGEgZnJlc2ggY29ubmVjdGlvblxuICAgICAgICAgICAgICAgICAgICB0aGlzLl9zdGFydE9yQXV0aFNzZSh7IHJlc3VtcHRpb25Ub2tlbjogdW5kZWZpbmVkIH0pLmNhdGNoKGVyciA9PiB7IHZhciBfYTsgcmV0dXJuIChfYSA9IHRoaXMub25lcnJvcikgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hLmNhbGwodGhpcywgZXJyKTsgfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIEdldCBvcmlnaW5hbCBtZXNzYWdlKHMpIGZvciBkZXRlY3RpbmcgcmVxdWVzdCBJRHNcbiAgICAgICAgICAgIGNvbnN0IG1lc3NhZ2VzID0gQXJyYXkuaXNBcnJheShtZXNzYWdlKSA/IG1lc3NhZ2UgOiBbbWVzc2FnZV07XG4gICAgICAgICAgICBjb25zdCBoYXNSZXF1ZXN0cyA9IG1lc3NhZ2VzLmZpbHRlcihtc2cgPT4gXCJtZXRob2RcIiBpbiBtc2cgJiYgXCJpZFwiIGluIG1zZyAmJiBtc2cuaWQgIT09IHVuZGVmaW5lZCkubGVuZ3RoID4gMDtcbiAgICAgICAgICAgIC8vIENoZWNrIHRoZSByZXNwb25zZSB0eXBlXG4gICAgICAgICAgICBjb25zdCBjb250ZW50VHlwZSA9IHJlc3BvbnNlLmhlYWRlcnMuZ2V0KFwiY29udGVudC10eXBlXCIpO1xuICAgICAgICAgICAgaWYgKGhhc1JlcXVlc3RzKSB7XG4gICAgICAgICAgICAgICAgaWYgKGNvbnRlbnRUeXBlID09PSBudWxsIHx8IGNvbnRlbnRUeXBlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBjb250ZW50VHlwZS5pbmNsdWRlcyhcInRleHQvZXZlbnQtc3RyZWFtXCIpKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIEhhbmRsZSBTU0Ugc3RyZWFtIHJlc3BvbnNlcyBmb3IgcmVxdWVzdHNcbiAgICAgICAgICAgICAgICAgICAgLy8gV2UgdXNlIHRoZSBzYW1lIGhhbmRsZXIgYXMgc3RhbmRhbG9uZSBzdHJlYW1zLCB3aGljaCBub3cgc3VwcG9ydHNcbiAgICAgICAgICAgICAgICAgICAgLy8gcmVjb25uZWN0aW9uIHdpdGggdGhlIGxhc3QgZXZlbnQgSURcbiAgICAgICAgICAgICAgICAgICAgdGhpcy5faGFuZGxlU3NlU3RyZWFtKHJlc3BvbnNlLmJvZHksIHsgb25yZXN1bXB0aW9udG9rZW4gfSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGNvbnRlbnRUeXBlID09PSBudWxsIHx8IGNvbnRlbnRUeXBlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBjb250ZW50VHlwZS5pbmNsdWRlcyhcImFwcGxpY2F0aW9uL2pzb25cIikpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8gRm9yIG5vbi1zdHJlYW1pbmcgc2VydmVycywgd2UgbWlnaHQgZ2V0IGRpcmVjdCBKU09OIHJlc3BvbnNlc1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCByZXNwb25zZU1lc3NhZ2VzID0gQXJyYXkuaXNBcnJheShkYXRhKVxuICAgICAgICAgICAgICAgICAgICAgICAgPyBkYXRhLm1hcChtc2cgPT4gSlNPTlJQQ01lc3NhZ2VTY2hlbWEucGFyc2UobXNnKSlcbiAgICAgICAgICAgICAgICAgICAgICAgIDogW0pTT05SUENNZXNzYWdlU2NoZW1hLnBhcnNlKGRhdGEpXTtcbiAgICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBtc2cgb2YgcmVzcG9uc2VNZXNzYWdlcykge1xuICAgICAgICAgICAgICAgICAgICAgICAgKF9iID0gdGhpcy5vbm1lc3NhZ2UpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5jYWxsKHRoaXMsIG1zZyk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBTdHJlYW1hYmxlSFRUUEVycm9yKC0xLCBgVW5leHBlY3RlZCBjb250ZW50IHR5cGU6ICR7Y29udGVudFR5cGV9YCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgKF9jID0gdGhpcy5vbmVycm9yKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MuY2FsbCh0aGlzLCBlcnJvcik7XG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgIH1cbiAgICBnZXQgc2Vzc2lvbklkKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5fc2Vzc2lvbklkO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBUZXJtaW5hdGVzIHRoZSBjdXJyZW50IHNlc3Npb24gYnkgc2VuZGluZyBhIERFTEVURSByZXF1ZXN0IHRvIHRoZSBzZXJ2ZXIuXG4gICAgICpcbiAgICAgKiBDbGllbnRzIHRoYXQgbm8gbG9uZ2VyIG5lZWQgYSBwYXJ0aWN1bGFyIHNlc3Npb25cbiAgICAgKiAoZS5nLiwgYmVjYXVzZSB0aGUgdXNlciBpcyBsZWF2aW5nIHRoZSBjbGllbnQgYXBwbGljYXRpb24pIFNIT1VMRCBzZW5kIGFuXG4gICAgICogSFRUUCBERUxFVEUgdG8gdGhlIE1DUCBlbmRwb2ludCB3aXRoIHRoZSBNY3AtU2Vzc2lvbi1JZCBoZWFkZXIgdG8gZXhwbGljaXRseVxuICAgICAqIHRlcm1pbmF0ZSB0aGUgc2Vzc2lvbi5cbiAgICAgKlxuICAgICAqIFRoZSBzZXJ2ZXIgTUFZIHJlc3BvbmQgd2l0aCBIVFRQIDQwNSBNZXRob2QgTm90IEFsbG93ZWQsIGluZGljYXRpbmcgdGhhdFxuICAgICAqIHRoZSBzZXJ2ZXIgZG9lcyBub3QgYWxsb3cgY2xpZW50cyB0byB0ZXJtaW5hdGUgc2Vzc2lvbnMuXG4gICAgICovXG4gICAgYXN5bmMgdGVybWluYXRlU2Vzc2lvbigpIHtcbiAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgaWYgKCF0aGlzLl9zZXNzaW9uSWQpIHtcbiAgICAgICAgICAgIHJldHVybjsgLy8gTm8gc2Vzc2lvbiB0byB0ZXJtaW5hdGVcbiAgICAgICAgfVxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgY29uc3QgaGVhZGVycyA9IGF3YWl0IHRoaXMuX2NvbW1vbkhlYWRlcnMoKTtcbiAgICAgICAgICAgIGNvbnN0IGluaXQgPSB7XG4gICAgICAgICAgICAgICAgLi4udGhpcy5fcmVxdWVzdEluaXQsXG4gICAgICAgICAgICAgICAgbWV0aG9kOiBcIkRFTEVURVwiLFxuICAgICAgICAgICAgICAgIGhlYWRlcnMsXG4gICAgICAgICAgICAgICAgc2lnbmFsOiAoX2EgPSB0aGlzLl9hYm9ydENvbnRyb2xsZXIpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5zaWduYWwsXG4gICAgICAgICAgICB9O1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh0aGlzLl91cmwsIGluaXQpO1xuICAgICAgICAgICAgLy8gV2Ugc3BlY2lmaWNhbGx5IGhhbmRsZSA0MDUgYXMgYSB2YWxpZCByZXNwb25zZSBhY2NvcmRpbmcgdG8gdGhlIHNwZWMsXG4gICAgICAgICAgICAvLyBtZWFuaW5nIHRoZSBzZXJ2ZXIgZG9lcyBub3Qgc3VwcG9ydCBleHBsaWNpdCBzZXNzaW9uIHRlcm1pbmF0aW9uXG4gICAgICAgICAgICBpZiAoIXJlc3BvbnNlLm9rICYmIHJlc3BvbnNlLnN0YXR1cyAhPT0gNDA1KSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IFN0cmVhbWFibGVIVFRQRXJyb3IocmVzcG9uc2Uuc3RhdHVzLCBgRmFpbGVkIHRvIHRlcm1pbmF0ZSBzZXNzaW9uOiAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLl9zZXNzaW9uSWQgPSB1bmRlZmluZWQ7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAoX2IgPSB0aGlzLm9uZXJyb3IpID09PSBudWxsIHx8IF9iID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYi5jYWxsKHRoaXMsIGVycm9yKTtcbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3RyZWFtYWJsZUh0dHAuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/client/streamableHttp.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/auth.js":
/*!************************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/auth.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OAuthClientInformationFullSchema: () => (/* binding */ OAuthClientInformationFullSchema),\n/* harmony export */   OAuthClientInformationSchema: () => (/* binding */ OAuthClientInformationSchema),\n/* harmony export */   OAuthClientMetadataSchema: () => (/* binding */ OAuthClientMetadataSchema),\n/* harmony export */   OAuthClientRegistrationErrorSchema: () => (/* binding */ OAuthClientRegistrationErrorSchema),\n/* harmony export */   OAuthErrorResponseSchema: () => (/* binding */ OAuthErrorResponseSchema),\n/* harmony export */   OAuthMetadataSchema: () => (/* binding */ OAuthMetadataSchema),\n/* harmony export */   OAuthProtectedResourceMetadataSchema: () => (/* binding */ OAuthProtectedResourceMetadataSchema),\n/* harmony export */   OAuthTokenRevocationRequestSchema: () => (/* binding */ OAuthTokenRevocationRequestSchema),\n/* harmony export */   OAuthTokensSchema: () => (/* binding */ OAuthTokensSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n/**\n * RFC 9728 OAuth Protected Resource Metadata\n */\nconst OAuthProtectedResourceMetadataSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    resource: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url(),\n    authorization_servers: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url()).optional(),\n    jwks_uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    scopes_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n    bearer_methods_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n    resource_signing_alg_values_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n    resource_name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    resource_documentation: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    resource_policy_uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    resource_tos_uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().url().optional(),\n    tls_client_certificate_bound_access_tokens: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().optional(),\n    authorization_details_types_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n    dpop_signing_alg_values_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n    dpop_bound_access_tokens_required: zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean().optional(),\n})\n    .passthrough();\n/**\n * RFC 8414 OAuth 2.0 Authorization Server Metadata\n */\nconst OAuthMetadataSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    issuer: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    authorization_endpoint: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    token_endpoint: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    registration_endpoint: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    scopes_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n    response_types_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n    response_modes_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n    grant_types_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n    token_endpoint_auth_methods_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n    token_endpoint_auth_signing_alg_values_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string())\n        .optional(),\n    service_documentation: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    revocation_endpoint: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    revocation_endpoint_auth_methods_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n    revocation_endpoint_auth_signing_alg_values_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string())\n        .optional(),\n    introspection_endpoint: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    introspection_endpoint_auth_methods_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string())\n        .optional(),\n    introspection_endpoint_auth_signing_alg_values_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string())\n        .optional(),\n    code_challenge_methods_supported: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n})\n    .passthrough();\n/**\n * OAuth 2.1 token response\n */\nconst OAuthTokensSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    access_token: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    token_type: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    expires_in: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().optional(),\n    scope: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    refresh_token: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n})\n    .strip();\n/**\n * OAuth 2.1 error response\n */\nconst OAuthErrorResponseSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    error: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    error_description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    error_uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n});\n/**\n * RFC 7591 OAuth 2.0 Dynamic Client Registration metadata\n */\nconst OAuthClientMetadataSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    redirect_uris: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).refine((uris) => uris.every((uri) => URL.canParse(uri)), { message: \"redirect_uris must contain valid URLs\" }),\n    token_endpoint_auth_method: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    grant_types: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n    response_types: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n    client_name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    client_uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    logo_uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    scope: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    contacts: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).optional(),\n    tos_uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    policy_uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    jwks_uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    jwks: zod__WEBPACK_IMPORTED_MODULE_0__.z.any().optional(),\n    software_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    software_version: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n}).strip();\n/**\n * RFC 7591 OAuth 2.0 Dynamic Client Registration client information\n */\nconst OAuthClientInformationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    client_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    client_secret: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    client_id_issued_at: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().optional(),\n    client_secret_expires_at: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().optional(),\n}).strip();\n/**\n * RFC 7591 OAuth 2.0 Dynamic Client Registration full response (client information plus metadata)\n */\nconst OAuthClientInformationFullSchema = OAuthClientMetadataSchema.merge(OAuthClientInformationSchema);\n/**\n * RFC 7591 OAuth 2.0 Dynamic Client Registration error response\n */\nconst OAuthClientRegistrationErrorSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    error: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    error_description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n}).strip();\n/**\n * RFC 7009 OAuth 2.0 Token Revocation request\n */\nconst OAuthTokenRevocationRequestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    token: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    token_type_hint: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n}).strip();\n//# sourceMappingURL=auth.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/auth.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/protocol.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/protocol.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_REQUEST_TIMEOUT_MSEC: () => (/* binding */ DEFAULT_REQUEST_TIMEOUT_MSEC),\n/* harmony export */   Protocol: () => (/* binding */ Protocol),\n/* harmony export */   mergeCapabilities: () => (/* binding */ mergeCapabilities)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js\");\n\n/**\n * The default request timeout, in miliseconds.\n */\nconst DEFAULT_REQUEST_TIMEOUT_MSEC = 60000;\n/**\n * Implements MCP protocol framing on top of a pluggable transport, including\n * features like request/response linking, notifications, and progress.\n */\nclass Protocol {\n    constructor(_options) {\n        this._options = _options;\n        this._requestMessageId = 0;\n        this._requestHandlers = new Map();\n        this._requestHandlerAbortControllers = new Map();\n        this._notificationHandlers = new Map();\n        this._responseHandlers = new Map();\n        this._progressHandlers = new Map();\n        this._timeoutInfo = new Map();\n        this.setNotificationHandler(_types_js__WEBPACK_IMPORTED_MODULE_0__.CancelledNotificationSchema, (notification) => {\n            const controller = this._requestHandlerAbortControllers.get(notification.params.requestId);\n            controller === null || controller === void 0 ? void 0 : controller.abort(notification.params.reason);\n        });\n        this.setNotificationHandler(_types_js__WEBPACK_IMPORTED_MODULE_0__.ProgressNotificationSchema, (notification) => {\n            this._onprogress(notification);\n        });\n        this.setRequestHandler(_types_js__WEBPACK_IMPORTED_MODULE_0__.PingRequestSchema, \n        // Automatic pong by default.\n        (_request) => ({}));\n    }\n    _setupTimeout(messageId, timeout, maxTotalTimeout, onTimeout, resetTimeoutOnProgress = false) {\n        this._timeoutInfo.set(messageId, {\n            timeoutId: setTimeout(onTimeout, timeout),\n            startTime: Date.now(),\n            timeout,\n            maxTotalTimeout,\n            resetTimeoutOnProgress,\n            onTimeout\n        });\n    }\n    _resetTimeout(messageId) {\n        const info = this._timeoutInfo.get(messageId);\n        if (!info)\n            return false;\n        const totalElapsed = Date.now() - info.startTime;\n        if (info.maxTotalTimeout && totalElapsed >= info.maxTotalTimeout) {\n            this._timeoutInfo.delete(messageId);\n            throw new _types_js__WEBPACK_IMPORTED_MODULE_0__.McpError(_types_js__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.RequestTimeout, \"Maximum total timeout exceeded\", { maxTotalTimeout: info.maxTotalTimeout, totalElapsed });\n        }\n        clearTimeout(info.timeoutId);\n        info.timeoutId = setTimeout(info.onTimeout, info.timeout);\n        return true;\n    }\n    _cleanupTimeout(messageId) {\n        const info = this._timeoutInfo.get(messageId);\n        if (info) {\n            clearTimeout(info.timeoutId);\n            this._timeoutInfo.delete(messageId);\n        }\n    }\n    /**\n     * Attaches to the given transport, starts it, and starts listening for messages.\n     *\n     * The Protocol object assumes ownership of the Transport, replacing any callbacks that have already been set, and expects that it is the only user of the Transport instance going forward.\n     */\n    async connect(transport) {\n        this._transport = transport;\n        this._transport.onclose = () => {\n            this._onclose();\n        };\n        this._transport.onerror = (error) => {\n            this._onerror(error);\n        };\n        this._transport.onmessage = (message, extra) => {\n            if ((0,_types_js__WEBPACK_IMPORTED_MODULE_0__.isJSONRPCResponse)(message) || (0,_types_js__WEBPACK_IMPORTED_MODULE_0__.isJSONRPCError)(message)) {\n                this._onresponse(message);\n            }\n            else if ((0,_types_js__WEBPACK_IMPORTED_MODULE_0__.isJSONRPCRequest)(message)) {\n                this._onrequest(message, extra);\n            }\n            else if ((0,_types_js__WEBPACK_IMPORTED_MODULE_0__.isJSONRPCNotification)(message)) {\n                this._onnotification(message);\n            }\n            else {\n                this._onerror(new Error(`Unknown message type: ${JSON.stringify(message)}`));\n            }\n        };\n        await this._transport.start();\n    }\n    _onclose() {\n        var _a;\n        const responseHandlers = this._responseHandlers;\n        this._responseHandlers = new Map();\n        this._progressHandlers.clear();\n        this._transport = undefined;\n        (_a = this.onclose) === null || _a === void 0 ? void 0 : _a.call(this);\n        const error = new _types_js__WEBPACK_IMPORTED_MODULE_0__.McpError(_types_js__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.ConnectionClosed, \"Connection closed\");\n        for (const handler of responseHandlers.values()) {\n            handler(error);\n        }\n    }\n    _onerror(error) {\n        var _a;\n        (_a = this.onerror) === null || _a === void 0 ? void 0 : _a.call(this, error);\n    }\n    _onnotification(notification) {\n        var _a;\n        const handler = (_a = this._notificationHandlers.get(notification.method)) !== null && _a !== void 0 ? _a : this.fallbackNotificationHandler;\n        // Ignore notifications not being subscribed to.\n        if (handler === undefined) {\n            return;\n        }\n        // Starting with Promise.resolve() puts any synchronous errors into the monad as well.\n        Promise.resolve()\n            .then(() => handler(notification))\n            .catch((error) => this._onerror(new Error(`Uncaught error in notification handler: ${error}`)));\n    }\n    _onrequest(request, extra) {\n        var _a, _b, _c, _d;\n        const handler = (_a = this._requestHandlers.get(request.method)) !== null && _a !== void 0 ? _a : this.fallbackRequestHandler;\n        if (handler === undefined) {\n            (_b = this._transport) === null || _b === void 0 ? void 0 : _b.send({\n                jsonrpc: \"2.0\",\n                id: request.id,\n                error: {\n                    code: _types_js__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.MethodNotFound,\n                    message: \"Method not found\",\n                },\n            }).catch((error) => this._onerror(new Error(`Failed to send an error response: ${error}`)));\n            return;\n        }\n        const abortController = new AbortController();\n        this._requestHandlerAbortControllers.set(request.id, abortController);\n        const fullExtra = {\n            signal: abortController.signal,\n            sessionId: (_c = this._transport) === null || _c === void 0 ? void 0 : _c.sessionId,\n            _meta: (_d = request.params) === null || _d === void 0 ? void 0 : _d._meta,\n            sendNotification: (notification) => this.notification(notification, { relatedRequestId: request.id }),\n            sendRequest: (r, resultSchema, options) => this.request(r, resultSchema, { ...options, relatedRequestId: request.id }),\n            authInfo: extra === null || extra === void 0 ? void 0 : extra.authInfo,\n            requestId: request.id,\n        };\n        // Starting with Promise.resolve() puts any synchronous errors into the monad as well.\n        Promise.resolve()\n            .then(() => handler(request, fullExtra))\n            .then((result) => {\n            var _a;\n            if (abortController.signal.aborted) {\n                return;\n            }\n            return (_a = this._transport) === null || _a === void 0 ? void 0 : _a.send({\n                result,\n                jsonrpc: \"2.0\",\n                id: request.id,\n            });\n        }, (error) => {\n            var _a, _b;\n            if (abortController.signal.aborted) {\n                return;\n            }\n            return (_a = this._transport) === null || _a === void 0 ? void 0 : _a.send({\n                jsonrpc: \"2.0\",\n                id: request.id,\n                error: {\n                    code: Number.isSafeInteger(error[\"code\"])\n                        ? error[\"code\"]\n                        : _types_js__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.InternalError,\n                    message: (_b = error.message) !== null && _b !== void 0 ? _b : \"Internal error\",\n                },\n            });\n        })\n            .catch((error) => this._onerror(new Error(`Failed to send response: ${error}`)))\n            .finally(() => {\n            this._requestHandlerAbortControllers.delete(request.id);\n        });\n    }\n    _onprogress(notification) {\n        const { progressToken, ...params } = notification.params;\n        const messageId = Number(progressToken);\n        const handler = this._progressHandlers.get(messageId);\n        if (!handler) {\n            this._onerror(new Error(`Received a progress notification for an unknown token: ${JSON.stringify(notification)}`));\n            return;\n        }\n        const responseHandler = this._responseHandlers.get(messageId);\n        const timeoutInfo = this._timeoutInfo.get(messageId);\n        if (timeoutInfo && responseHandler && timeoutInfo.resetTimeoutOnProgress) {\n            try {\n                this._resetTimeout(messageId);\n            }\n            catch (error) {\n                responseHandler(error);\n                return;\n            }\n        }\n        handler(params);\n    }\n    _onresponse(response) {\n        const messageId = Number(response.id);\n        const handler = this._responseHandlers.get(messageId);\n        if (handler === undefined) {\n            this._onerror(new Error(`Received a response for an unknown message ID: ${JSON.stringify(response)}`));\n            return;\n        }\n        this._responseHandlers.delete(messageId);\n        this._progressHandlers.delete(messageId);\n        this._cleanupTimeout(messageId);\n        if ((0,_types_js__WEBPACK_IMPORTED_MODULE_0__.isJSONRPCResponse)(response)) {\n            handler(response);\n        }\n        else {\n            const error = new _types_js__WEBPACK_IMPORTED_MODULE_0__.McpError(response.error.code, response.error.message, response.error.data);\n            handler(error);\n        }\n    }\n    get transport() {\n        return this._transport;\n    }\n    /**\n     * Closes the connection.\n     */\n    async close() {\n        var _a;\n        await ((_a = this._transport) === null || _a === void 0 ? void 0 : _a.close());\n    }\n    /**\n     * Sends a request and wait for a response.\n     *\n     * Do not use this method to emit notifications! Use notification() instead.\n     */\n    request(request, resultSchema, options) {\n        const { relatedRequestId, resumptionToken, onresumptiontoken } = options !== null && options !== void 0 ? options : {};\n        return new Promise((resolve, reject) => {\n            var _a, _b, _c, _d, _e, _f;\n            if (!this._transport) {\n                reject(new Error(\"Not connected\"));\n                return;\n            }\n            if (((_a = this._options) === null || _a === void 0 ? void 0 : _a.enforceStrictCapabilities) === true) {\n                this.assertCapabilityForMethod(request.method);\n            }\n            (_b = options === null || options === void 0 ? void 0 : options.signal) === null || _b === void 0 ? void 0 : _b.throwIfAborted();\n            const messageId = this._requestMessageId++;\n            const jsonrpcRequest = {\n                ...request,\n                jsonrpc: \"2.0\",\n                id: messageId,\n            };\n            if (options === null || options === void 0 ? void 0 : options.onprogress) {\n                this._progressHandlers.set(messageId, options.onprogress);\n                jsonrpcRequest.params = {\n                    ...request.params,\n                    _meta: {\n                        ...(((_c = request.params) === null || _c === void 0 ? void 0 : _c._meta) || {}),\n                        progressToken: messageId\n                    },\n                };\n            }\n            const cancel = (reason) => {\n                var _a;\n                this._responseHandlers.delete(messageId);\n                this._progressHandlers.delete(messageId);\n                this._cleanupTimeout(messageId);\n                (_a = this._transport) === null || _a === void 0 ? void 0 : _a.send({\n                    jsonrpc: \"2.0\",\n                    method: \"notifications/cancelled\",\n                    params: {\n                        requestId: messageId,\n                        reason: String(reason),\n                    },\n                }, { relatedRequestId, resumptionToken, onresumptiontoken }).catch((error) => this._onerror(new Error(`Failed to send cancellation: ${error}`)));\n                reject(reason);\n            };\n            this._responseHandlers.set(messageId, (response) => {\n                var _a;\n                if ((_a = options === null || options === void 0 ? void 0 : options.signal) === null || _a === void 0 ? void 0 : _a.aborted) {\n                    return;\n                }\n                if (response instanceof Error) {\n                    return reject(response);\n                }\n                try {\n                    const result = resultSchema.parse(response.result);\n                    resolve(result);\n                }\n                catch (error) {\n                    reject(error);\n                }\n            });\n            (_d = options === null || options === void 0 ? void 0 : options.signal) === null || _d === void 0 ? void 0 : _d.addEventListener(\"abort\", () => {\n                var _a;\n                cancel((_a = options === null || options === void 0 ? void 0 : options.signal) === null || _a === void 0 ? void 0 : _a.reason);\n            });\n            const timeout = (_e = options === null || options === void 0 ? void 0 : options.timeout) !== null && _e !== void 0 ? _e : DEFAULT_REQUEST_TIMEOUT_MSEC;\n            const timeoutHandler = () => cancel(new _types_js__WEBPACK_IMPORTED_MODULE_0__.McpError(_types_js__WEBPACK_IMPORTED_MODULE_0__.ErrorCode.RequestTimeout, \"Request timed out\", { timeout }));\n            this._setupTimeout(messageId, timeout, options === null || options === void 0 ? void 0 : options.maxTotalTimeout, timeoutHandler, (_f = options === null || options === void 0 ? void 0 : options.resetTimeoutOnProgress) !== null && _f !== void 0 ? _f : false);\n            this._transport.send(jsonrpcRequest, { relatedRequestId, resumptionToken, onresumptiontoken }).catch((error) => {\n                this._cleanupTimeout(messageId);\n                reject(error);\n            });\n        });\n    }\n    /**\n     * Emits a notification, which is a one-way message that does not expect a response.\n     */\n    async notification(notification, options) {\n        if (!this._transport) {\n            throw new Error(\"Not connected\");\n        }\n        this.assertNotificationCapability(notification.method);\n        const jsonrpcNotification = {\n            ...notification,\n            jsonrpc: \"2.0\",\n        };\n        await this._transport.send(jsonrpcNotification, options);\n    }\n    /**\n     * Registers a handler to invoke when this protocol object receives a request with the given method.\n     *\n     * Note that this will replace any previous request handler for the same method.\n     */\n    setRequestHandler(requestSchema, handler) {\n        const method = requestSchema.shape.method.value;\n        this.assertRequestHandlerCapability(method);\n        this._requestHandlers.set(method, (request, extra) => {\n            return Promise.resolve(handler(requestSchema.parse(request), extra));\n        });\n    }\n    /**\n     * Removes the request handler for the given method.\n     */\n    removeRequestHandler(method) {\n        this._requestHandlers.delete(method);\n    }\n    /**\n     * Asserts that a request handler has not already been set for the given method, in preparation for a new one being automatically installed.\n     */\n    assertCanSetRequestHandler(method) {\n        if (this._requestHandlers.has(method)) {\n            throw new Error(`A request handler for ${method} already exists, which would be overridden`);\n        }\n    }\n    /**\n     * Registers a handler to invoke when this protocol object receives a notification with the given method.\n     *\n     * Note that this will replace any previous notification handler for the same method.\n     */\n    setNotificationHandler(notificationSchema, handler) {\n        this._notificationHandlers.set(notificationSchema.shape.method.value, (notification) => Promise.resolve(handler(notificationSchema.parse(notification))));\n    }\n    /**\n     * Removes the notification handler for the given method.\n     */\n    removeNotificationHandler(method) {\n        this._notificationHandlers.delete(method);\n    }\n}\nfunction mergeCapabilities(base, additional) {\n    return Object.entries(additional).reduce((acc, [key, value]) => {\n        if (value && typeof value === \"object\") {\n            acc[key] = acc[key] ? { ...acc[key], ...value } : value;\n        }\n        else {\n            acc[key] = value;\n        }\n        return acc;\n    }, { ...base });\n}\n//# sourceMappingURL=protocol.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/protocol.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/stdio.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/stdio.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReadBuffer: () => (/* binding */ ReadBuffer),\n/* harmony export */   deserializeMessage: () => (/* binding */ deserializeMessage),\n/* harmony export */   serializeMessage: () => (/* binding */ serializeMessage)\n/* harmony export */ });\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../types.js */ \"(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js\");\n\n/**\n * Buffers a continuous stdio stream into discrete JSON-RPC messages.\n */\nclass ReadBuffer {\n    append(chunk) {\n        this._buffer = this._buffer ? Buffer.concat([this._buffer, chunk]) : chunk;\n    }\n    readMessage() {\n        if (!this._buffer) {\n            return null;\n        }\n        const index = this._buffer.indexOf(\"\\n\");\n        if (index === -1) {\n            return null;\n        }\n        const line = this._buffer.toString(\"utf8\", 0, index).replace(/\\r$/, '');\n        this._buffer = this._buffer.subarray(index + 1);\n        return deserializeMessage(line);\n    }\n    clear() {\n        this._buffer = undefined;\n    }\n}\nfunction deserializeMessage(line) {\n    return _types_js__WEBPACK_IMPORTED_MODULE_0__.JSONRPCMessageSchema.parse(JSON.parse(line));\n}\nfunction serializeMessage(message) {\n    return JSON.stringify(message) + \"\\n\";\n}\n//# sourceMappingURL=stdio.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG1vZGVsY29udGV4dHByb3RvY29sL3Nkay9kaXN0L2VzbS9zaGFyZWQvc3RkaW8uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFtRDtBQUNuRDtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUCxXQUFXLDJEQUFvQjtBQUMvQjtBQUNPO0FBQ1A7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEBtb2RlbGNvbnRleHRwcm90b2NvbFxcc2RrXFxkaXN0XFxlc21cXHNoYXJlZFxcc3RkaW8uanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSlNPTlJQQ01lc3NhZ2VTY2hlbWEgfSBmcm9tIFwiLi4vdHlwZXMuanNcIjtcbi8qKlxuICogQnVmZmVycyBhIGNvbnRpbnVvdXMgc3RkaW8gc3RyZWFtIGludG8gZGlzY3JldGUgSlNPTi1SUEMgbWVzc2FnZXMuXG4gKi9cbmV4cG9ydCBjbGFzcyBSZWFkQnVmZmVyIHtcbiAgICBhcHBlbmQoY2h1bmspIHtcbiAgICAgICAgdGhpcy5fYnVmZmVyID0gdGhpcy5fYnVmZmVyID8gQnVmZmVyLmNvbmNhdChbdGhpcy5fYnVmZmVyLCBjaHVua10pIDogY2h1bms7XG4gICAgfVxuICAgIHJlYWRNZXNzYWdlKCkge1xuICAgICAgICBpZiAoIXRoaXMuX2J1ZmZlcikge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgaW5kZXggPSB0aGlzLl9idWZmZXIuaW5kZXhPZihcIlxcblwiKTtcbiAgICAgICAgaWYgKGluZGV4ID09PSAtMSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgbGluZSA9IHRoaXMuX2J1ZmZlci50b1N0cmluZyhcInV0ZjhcIiwgMCwgaW5kZXgpLnJlcGxhY2UoL1xcciQvLCAnJyk7XG4gICAgICAgIHRoaXMuX2J1ZmZlciA9IHRoaXMuX2J1ZmZlci5zdWJhcnJheShpbmRleCArIDEpO1xuICAgICAgICByZXR1cm4gZGVzZXJpYWxpemVNZXNzYWdlKGxpbmUpO1xuICAgIH1cbiAgICBjbGVhcigpIHtcbiAgICAgICAgdGhpcy5fYnVmZmVyID0gdW5kZWZpbmVkO1xuICAgIH1cbn1cbmV4cG9ydCBmdW5jdGlvbiBkZXNlcmlhbGl6ZU1lc3NhZ2UobGluZSkge1xuICAgIHJldHVybiBKU09OUlBDTWVzc2FnZVNjaGVtYS5wYXJzZShKU09OLnBhcnNlKGxpbmUpKTtcbn1cbmV4cG9ydCBmdW5jdGlvbiBzZXJpYWxpemVNZXNzYWdlKG1lc3NhZ2UpIHtcbiAgICByZXR1cm4gSlNPTi5zdHJpbmdpZnkobWVzc2FnZSkgKyBcIlxcblwiO1xufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9c3RkaW8uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/shared/stdio.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js":
/*!******************************************************************!*\
  !*** ./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AudioContentSchema: () => (/* binding */ AudioContentSchema),\n/* harmony export */   BlobResourceContentsSchema: () => (/* binding */ BlobResourceContentsSchema),\n/* harmony export */   CallToolRequestSchema: () => (/* binding */ CallToolRequestSchema),\n/* harmony export */   CallToolResultSchema: () => (/* binding */ CallToolResultSchema),\n/* harmony export */   CancelledNotificationSchema: () => (/* binding */ CancelledNotificationSchema),\n/* harmony export */   ClientCapabilitiesSchema: () => (/* binding */ ClientCapabilitiesSchema),\n/* harmony export */   ClientNotificationSchema: () => (/* binding */ ClientNotificationSchema),\n/* harmony export */   ClientRequestSchema: () => (/* binding */ ClientRequestSchema),\n/* harmony export */   ClientResultSchema: () => (/* binding */ ClientResultSchema),\n/* harmony export */   CompatibilityCallToolResultSchema: () => (/* binding */ CompatibilityCallToolResultSchema),\n/* harmony export */   CompleteRequestSchema: () => (/* binding */ CompleteRequestSchema),\n/* harmony export */   CompleteResultSchema: () => (/* binding */ CompleteResultSchema),\n/* harmony export */   CreateMessageRequestSchema: () => (/* binding */ CreateMessageRequestSchema),\n/* harmony export */   CreateMessageResultSchema: () => (/* binding */ CreateMessageResultSchema),\n/* harmony export */   CursorSchema: () => (/* binding */ CursorSchema),\n/* harmony export */   EmbeddedResourceSchema: () => (/* binding */ EmbeddedResourceSchema),\n/* harmony export */   EmptyResultSchema: () => (/* binding */ EmptyResultSchema),\n/* harmony export */   ErrorCode: () => (/* binding */ ErrorCode),\n/* harmony export */   GetPromptRequestSchema: () => (/* binding */ GetPromptRequestSchema),\n/* harmony export */   GetPromptResultSchema: () => (/* binding */ GetPromptResultSchema),\n/* harmony export */   ImageContentSchema: () => (/* binding */ ImageContentSchema),\n/* harmony export */   ImplementationSchema: () => (/* binding */ ImplementationSchema),\n/* harmony export */   InitializeRequestSchema: () => (/* binding */ InitializeRequestSchema),\n/* harmony export */   InitializeResultSchema: () => (/* binding */ InitializeResultSchema),\n/* harmony export */   InitializedNotificationSchema: () => (/* binding */ InitializedNotificationSchema),\n/* harmony export */   JSONRPCErrorSchema: () => (/* binding */ JSONRPCErrorSchema),\n/* harmony export */   JSONRPCMessageSchema: () => (/* binding */ JSONRPCMessageSchema),\n/* harmony export */   JSONRPCNotificationSchema: () => (/* binding */ JSONRPCNotificationSchema),\n/* harmony export */   JSONRPCRequestSchema: () => (/* binding */ JSONRPCRequestSchema),\n/* harmony export */   JSONRPCResponseSchema: () => (/* binding */ JSONRPCResponseSchema),\n/* harmony export */   JSONRPC_VERSION: () => (/* binding */ JSONRPC_VERSION),\n/* harmony export */   LATEST_PROTOCOL_VERSION: () => (/* binding */ LATEST_PROTOCOL_VERSION),\n/* harmony export */   ListPromptsRequestSchema: () => (/* binding */ ListPromptsRequestSchema),\n/* harmony export */   ListPromptsResultSchema: () => (/* binding */ ListPromptsResultSchema),\n/* harmony export */   ListResourceTemplatesRequestSchema: () => (/* binding */ ListResourceTemplatesRequestSchema),\n/* harmony export */   ListResourceTemplatesResultSchema: () => (/* binding */ ListResourceTemplatesResultSchema),\n/* harmony export */   ListResourcesRequestSchema: () => (/* binding */ ListResourcesRequestSchema),\n/* harmony export */   ListResourcesResultSchema: () => (/* binding */ ListResourcesResultSchema),\n/* harmony export */   ListRootsRequestSchema: () => (/* binding */ ListRootsRequestSchema),\n/* harmony export */   ListRootsResultSchema: () => (/* binding */ ListRootsResultSchema),\n/* harmony export */   ListToolsRequestSchema: () => (/* binding */ ListToolsRequestSchema),\n/* harmony export */   ListToolsResultSchema: () => (/* binding */ ListToolsResultSchema),\n/* harmony export */   LoggingLevelSchema: () => (/* binding */ LoggingLevelSchema),\n/* harmony export */   LoggingMessageNotificationSchema: () => (/* binding */ LoggingMessageNotificationSchema),\n/* harmony export */   McpError: () => (/* binding */ McpError),\n/* harmony export */   ModelHintSchema: () => (/* binding */ ModelHintSchema),\n/* harmony export */   ModelPreferencesSchema: () => (/* binding */ ModelPreferencesSchema),\n/* harmony export */   NotificationSchema: () => (/* binding */ NotificationSchema),\n/* harmony export */   PaginatedRequestSchema: () => (/* binding */ PaginatedRequestSchema),\n/* harmony export */   PaginatedResultSchema: () => (/* binding */ PaginatedResultSchema),\n/* harmony export */   PingRequestSchema: () => (/* binding */ PingRequestSchema),\n/* harmony export */   ProgressNotificationSchema: () => (/* binding */ ProgressNotificationSchema),\n/* harmony export */   ProgressSchema: () => (/* binding */ ProgressSchema),\n/* harmony export */   ProgressTokenSchema: () => (/* binding */ ProgressTokenSchema),\n/* harmony export */   PromptArgumentSchema: () => (/* binding */ PromptArgumentSchema),\n/* harmony export */   PromptListChangedNotificationSchema: () => (/* binding */ PromptListChangedNotificationSchema),\n/* harmony export */   PromptMessageSchema: () => (/* binding */ PromptMessageSchema),\n/* harmony export */   PromptReferenceSchema: () => (/* binding */ PromptReferenceSchema),\n/* harmony export */   PromptSchema: () => (/* binding */ PromptSchema),\n/* harmony export */   ReadResourceRequestSchema: () => (/* binding */ ReadResourceRequestSchema),\n/* harmony export */   ReadResourceResultSchema: () => (/* binding */ ReadResourceResultSchema),\n/* harmony export */   RequestIdSchema: () => (/* binding */ RequestIdSchema),\n/* harmony export */   RequestSchema: () => (/* binding */ RequestSchema),\n/* harmony export */   ResourceContentsSchema: () => (/* binding */ ResourceContentsSchema),\n/* harmony export */   ResourceListChangedNotificationSchema: () => (/* binding */ ResourceListChangedNotificationSchema),\n/* harmony export */   ResourceReferenceSchema: () => (/* binding */ ResourceReferenceSchema),\n/* harmony export */   ResourceSchema: () => (/* binding */ ResourceSchema),\n/* harmony export */   ResourceTemplateSchema: () => (/* binding */ ResourceTemplateSchema),\n/* harmony export */   ResourceUpdatedNotificationSchema: () => (/* binding */ ResourceUpdatedNotificationSchema),\n/* harmony export */   ResultSchema: () => (/* binding */ ResultSchema),\n/* harmony export */   RootSchema: () => (/* binding */ RootSchema),\n/* harmony export */   RootsListChangedNotificationSchema: () => (/* binding */ RootsListChangedNotificationSchema),\n/* harmony export */   SUPPORTED_PROTOCOL_VERSIONS: () => (/* binding */ SUPPORTED_PROTOCOL_VERSIONS),\n/* harmony export */   SamplingMessageSchema: () => (/* binding */ SamplingMessageSchema),\n/* harmony export */   ServerCapabilitiesSchema: () => (/* binding */ ServerCapabilitiesSchema),\n/* harmony export */   ServerNotificationSchema: () => (/* binding */ ServerNotificationSchema),\n/* harmony export */   ServerRequestSchema: () => (/* binding */ ServerRequestSchema),\n/* harmony export */   ServerResultSchema: () => (/* binding */ ServerResultSchema),\n/* harmony export */   SetLevelRequestSchema: () => (/* binding */ SetLevelRequestSchema),\n/* harmony export */   SubscribeRequestSchema: () => (/* binding */ SubscribeRequestSchema),\n/* harmony export */   TextContentSchema: () => (/* binding */ TextContentSchema),\n/* harmony export */   TextResourceContentsSchema: () => (/* binding */ TextResourceContentsSchema),\n/* harmony export */   ToolAnnotationsSchema: () => (/* binding */ ToolAnnotationsSchema),\n/* harmony export */   ToolListChangedNotificationSchema: () => (/* binding */ ToolListChangedNotificationSchema),\n/* harmony export */   ToolSchema: () => (/* binding */ ToolSchema),\n/* harmony export */   UnsubscribeRequestSchema: () => (/* binding */ UnsubscribeRequestSchema),\n/* harmony export */   isInitializeRequest: () => (/* binding */ isInitializeRequest),\n/* harmony export */   isInitializedNotification: () => (/* binding */ isInitializedNotification),\n/* harmony export */   isJSONRPCError: () => (/* binding */ isJSONRPCError),\n/* harmony export */   isJSONRPCNotification: () => (/* binding */ isJSONRPCNotification),\n/* harmony export */   isJSONRPCRequest: () => (/* binding */ isJSONRPCRequest),\n/* harmony export */   isJSONRPCResponse: () => (/* binding */ isJSONRPCResponse)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\nconst LATEST_PROTOCOL_VERSION = \"2025-03-26\";\nconst SUPPORTED_PROTOCOL_VERSIONS = [\n    LATEST_PROTOCOL_VERSION,\n    \"2024-11-05\",\n    \"2024-10-07\",\n];\n/* JSON-RPC types */\nconst JSONRPC_VERSION = \"2.0\";\n/**\n * A progress token, used to associate progress notifications with the original request.\n */\nconst ProgressTokenSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int()]);\n/**\n * An opaque token used to represent a cursor for pagination.\n */\nconst CursorSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.string();\nconst RequestMetaSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * If specified, the caller is requesting out-of-band progress notifications for this request (as represented by notifications/progress). The value of this parameter is an opaque token that will be attached to any subsequent notifications. The receiver is not obligated to provide these notifications.\n     */\n    progressToken: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(ProgressTokenSchema),\n})\n    .passthrough();\nconst BaseRequestParamsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(RequestMetaSchema),\n})\n    .passthrough();\nconst RequestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    params: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(BaseRequestParamsSchema),\n});\nconst BaseNotificationParamsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * This parameter name is reserved by MCP to allow clients and servers to attach additional metadata to their notifications.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({}).passthrough()),\n})\n    .passthrough();\nconst NotificationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    params: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(BaseNotificationParamsSchema),\n});\nconst ResultSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * This result property is reserved by the protocol to allow clients and servers to attach additional metadata to their responses.\n     */\n    _meta: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({}).passthrough()),\n})\n    .passthrough();\n/**\n * A uniquely identifying ID for a request in JSON-RPC.\n */\nconst RequestIdSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([zod__WEBPACK_IMPORTED_MODULE_0__.z.string(), zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int()]);\n/**\n * A request that expects a response.\n */\nconst JSONRPCRequestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    jsonrpc: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(JSONRPC_VERSION),\n    id: RequestIdSchema,\n})\n    .merge(RequestSchema)\n    .strict();\nconst isJSONRPCRequest = (value) => JSONRPCRequestSchema.safeParse(value).success;\n/**\n * A notification which does not expect a response.\n */\nconst JSONRPCNotificationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    jsonrpc: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(JSONRPC_VERSION),\n})\n    .merge(NotificationSchema)\n    .strict();\nconst isJSONRPCNotification = (value) => JSONRPCNotificationSchema.safeParse(value).success;\n/**\n * A successful (non-error) response to a request.\n */\nconst JSONRPCResponseSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    jsonrpc: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(JSONRPC_VERSION),\n    id: RequestIdSchema,\n    result: ResultSchema,\n})\n    .strict();\nconst isJSONRPCResponse = (value) => JSONRPCResponseSchema.safeParse(value).success;\n/**\n * Error codes defined by the JSON-RPC specification.\n */\nvar ErrorCode;\n(function (ErrorCode) {\n    // SDK error codes\n    ErrorCode[ErrorCode[\"ConnectionClosed\"] = -32000] = \"ConnectionClosed\";\n    ErrorCode[ErrorCode[\"RequestTimeout\"] = -32001] = \"RequestTimeout\";\n    // Standard JSON-RPC error codes\n    ErrorCode[ErrorCode[\"ParseError\"] = -32700] = \"ParseError\";\n    ErrorCode[ErrorCode[\"InvalidRequest\"] = -32600] = \"InvalidRequest\";\n    ErrorCode[ErrorCode[\"MethodNotFound\"] = -32601] = \"MethodNotFound\";\n    ErrorCode[ErrorCode[\"InvalidParams\"] = -32602] = \"InvalidParams\";\n    ErrorCode[ErrorCode[\"InternalError\"] = -32603] = \"InternalError\";\n})(ErrorCode || (ErrorCode = {}));\n/**\n * A response to a request that indicates an error occurred.\n */\nconst JSONRPCErrorSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    jsonrpc: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(JSONRPC_VERSION),\n    id: RequestIdSchema,\n    error: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        /**\n         * The error type that occurred.\n         */\n        code: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int(),\n        /**\n         * A short description of the error. The message SHOULD be limited to a concise single sentence.\n         */\n        message: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n        /**\n         * Additional information about the error. The value of this member is defined by the sender (e.g. detailed error information, nested errors etc.).\n         */\n        data: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.unknown()),\n    }),\n})\n    .strict();\nconst isJSONRPCError = (value) => JSONRPCErrorSchema.safeParse(value).success;\nconst JSONRPCMessageSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    JSONRPCRequestSchema,\n    JSONRPCNotificationSchema,\n    JSONRPCResponseSchema,\n    JSONRPCErrorSchema,\n]);\n/* Empty result */\n/**\n * A response that indicates success but carries no data.\n */\nconst EmptyResultSchema = ResultSchema.strict();\n/* Cancellation */\n/**\n * This notification can be sent by either side to indicate that it is cancelling a previously-issued request.\n *\n * The request SHOULD still be in-flight, but due to communication latency, it is always possible that this notification MAY arrive after the request has already finished.\n *\n * This notification indicates that the result will be unused, so any associated processing SHOULD cease.\n *\n * A client MUST NOT attempt to cancel its `initialize` request.\n */\nconst CancelledNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"notifications/cancelled\"),\n    params: BaseNotificationParamsSchema.extend({\n        /**\n         * The ID of the request to cancel.\n         *\n         * This MUST correspond to the ID of a request previously issued in the same direction.\n         */\n        requestId: RequestIdSchema,\n        /**\n         * An optional string describing the reason for the cancellation. This MAY be logged or presented to the user.\n         */\n        reason: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    }),\n});\n/* Initialization */\n/**\n * Describes the name and version of an MCP implementation.\n */\nconst ImplementationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    version: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n})\n    .passthrough();\n/**\n * Capabilities a client may support. Known capabilities are defined here, in this schema, but this is not a closed set: any client can define its own, additional capabilities.\n */\nconst ClientCapabilitiesSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * Experimental, non-standard capabilities that the client supports.\n     */\n    experimental: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({}).passthrough()),\n    /**\n     * Present if the client supports sampling from an LLM.\n     */\n    sampling: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({}).passthrough()),\n    /**\n     * Present if the client supports listing roots.\n     */\n    roots: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        /**\n         * Whether the client supports issuing notifications for changes to the roots list.\n         */\n        listChanged: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()),\n    })\n        .passthrough()),\n})\n    .passthrough();\n/**\n * This request is sent from the client to the server when it first connects, asking it to begin initialization.\n */\nconst InitializeRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"initialize\"),\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * The latest version of the Model Context Protocol that the client supports. The client MAY decide to support older versions as well.\n         */\n        protocolVersion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n        capabilities: ClientCapabilitiesSchema,\n        clientInfo: ImplementationSchema,\n    }),\n});\nconst isInitializeRequest = (value) => InitializeRequestSchema.safeParse(value).success;\n/**\n * Capabilities that a server may support. Known capabilities are defined here, in this schema, but this is not a closed set: any server can define its own, additional capabilities.\n */\nconst ServerCapabilitiesSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * Experimental, non-standard capabilities that the server supports.\n     */\n    experimental: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({}).passthrough()),\n    /**\n     * Present if the server supports sending log messages to the client.\n     */\n    logging: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({}).passthrough()),\n    /**\n     * Present if the server supports sending completions to the client.\n     */\n    completions: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({}).passthrough()),\n    /**\n     * Present if the server offers any prompt templates.\n     */\n    prompts: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        /**\n         * Whether this server supports issuing notifications for changes to the prompt list.\n         */\n        listChanged: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()),\n    })\n        .passthrough()),\n    /**\n     * Present if the server offers any resources to read.\n     */\n    resources: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        /**\n         * Whether this server supports clients subscribing to resource updates.\n         */\n        subscribe: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()),\n        /**\n         * Whether this server supports issuing notifications for changes to the resource list.\n         */\n        listChanged: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()),\n    })\n        .passthrough()),\n    /**\n     * Present if the server offers any tools to call.\n     */\n    tools: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        /**\n         * Whether this server supports issuing notifications for changes to the tool list.\n         */\n        listChanged: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()),\n    })\n        .passthrough()),\n})\n    .passthrough();\n/**\n * After receiving an initialize request from the client, the server sends this response.\n */\nconst InitializeResultSchema = ResultSchema.extend({\n    /**\n     * The version of the Model Context Protocol that the server wants to use. This may not match the version that the client requested. If the client cannot support this version, it MUST disconnect.\n     */\n    protocolVersion: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    capabilities: ServerCapabilitiesSchema,\n    serverInfo: ImplementationSchema,\n    /**\n     * Instructions describing how to use the server and its features.\n     *\n     * This can be used by clients to improve the LLM's understanding of available tools, resources, etc. It can be thought of like a \"hint\" to the model. For example, this information MAY be added to the system prompt.\n     */\n    instructions: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n});\n/**\n * This notification is sent from the client to the server after initialization has finished.\n */\nconst InitializedNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"notifications/initialized\"),\n});\nconst isInitializedNotification = (value) => InitializedNotificationSchema.safeParse(value).success;\n/* Ping */\n/**\n * A ping, issued by either the server or the client, to check that the other party is still alive. The receiver must promptly respond, or else may be disconnected.\n */\nconst PingRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"ping\"),\n});\n/* Progress notifications */\nconst ProgressSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * The progress thus far. This should increase every time progress is made, even if the total is unknown.\n     */\n    progress: zod__WEBPACK_IMPORTED_MODULE_0__.z.number(),\n    /**\n     * Total number of items to process (or total progress required), if known.\n     */\n    total: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.number()),\n    /**\n     * An optional message describing the current progress.\n     */\n    message: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n})\n    .passthrough();\n/**\n * An out-of-band notification used to inform the receiver of a progress update for a long-running request.\n */\nconst ProgressNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"notifications/progress\"),\n    params: BaseNotificationParamsSchema.merge(ProgressSchema).extend({\n        /**\n         * The progress token which was given in the initial request, used to associate this notification with the request that is proceeding.\n         */\n        progressToken: ProgressTokenSchema,\n    }),\n});\n/* Pagination */\nconst PaginatedRequestSchema = RequestSchema.extend({\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * An opaque token representing the current pagination position.\n         * If provided, the server should return results starting after this cursor.\n         */\n        cursor: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(CursorSchema),\n    }).optional(),\n});\nconst PaginatedResultSchema = ResultSchema.extend({\n    /**\n     * An opaque token representing the pagination position after the last returned result.\n     * If present, there may be more results available.\n     */\n    nextCursor: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(CursorSchema),\n});\n/* Resources */\n/**\n * The contents of a specific resource or sub-resource.\n */\nconst ResourceContentsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * The URI of this resource.\n     */\n    uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    /**\n     * The MIME type of this resource, if known.\n     */\n    mimeType: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n})\n    .passthrough();\nconst TextResourceContentsSchema = ResourceContentsSchema.extend({\n    /**\n     * The text of the item. This must only be set if the item can actually be represented as text (not binary data).\n     */\n    text: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n});\nconst BlobResourceContentsSchema = ResourceContentsSchema.extend({\n    /**\n     * A base64-encoded string representing the binary data of the item.\n     */\n    blob: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().base64(),\n});\n/**\n * A known resource that the server is capable of reading.\n */\nconst ResourceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * The URI of this resource.\n     */\n    uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    /**\n     * A human-readable name for this resource.\n     *\n     * This can be used by clients to populate UI elements.\n     */\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    /**\n     * A description of what this resource represents.\n     *\n     * This can be used by clients to improve the LLM's understanding of available resources. It can be thought of like a \"hint\" to the model.\n     */\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n    /**\n     * The MIME type of this resource, if known.\n     */\n    mimeType: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n})\n    .passthrough();\n/**\n * A template description for resources available on the server.\n */\nconst ResourceTemplateSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * A URI template (according to RFC 6570) that can be used to construct resource URIs.\n     */\n    uriTemplate: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    /**\n     * A human-readable name for the type of resource this template refers to.\n     *\n     * This can be used by clients to populate UI elements.\n     */\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    /**\n     * A description of what this template is for.\n     *\n     * This can be used by clients to improve the LLM's understanding of available resources. It can be thought of like a \"hint\" to the model.\n     */\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n    /**\n     * The MIME type for all resources that match this template. This should only be included if all resources matching this template have the same type.\n     */\n    mimeType: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n})\n    .passthrough();\n/**\n * Sent from the client to request a list of resources the server has.\n */\nconst ListResourcesRequestSchema = PaginatedRequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"resources/list\"),\n});\n/**\n * The server's response to a resources/list request from the client.\n */\nconst ListResourcesResultSchema = PaginatedResultSchema.extend({\n    resources: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(ResourceSchema),\n});\n/**\n * Sent from the client to request a list of resource templates the server has.\n */\nconst ListResourceTemplatesRequestSchema = PaginatedRequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"resources/templates/list\"),\n});\n/**\n * The server's response to a resources/templates/list request from the client.\n */\nconst ListResourceTemplatesResultSchema = PaginatedResultSchema.extend({\n    resourceTemplates: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(ResourceTemplateSchema),\n});\n/**\n * Sent from the client to the server, to read a specific resource URI.\n */\nconst ReadResourceRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"resources/read\"),\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * The URI of the resource to read. The URI can use any protocol; it is up to the server how to interpret it.\n         */\n        uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    }),\n});\n/**\n * The server's response to a resources/read request from the client.\n */\nconst ReadResourceResultSchema = ResultSchema.extend({\n    contents: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.union([TextResourceContentsSchema, BlobResourceContentsSchema])),\n});\n/**\n * An optional notification from the server to the client, informing it that the list of resources it can read from has changed. This may be issued by servers without any previous subscription from the client.\n */\nconst ResourceListChangedNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"notifications/resources/list_changed\"),\n});\n/**\n * Sent from the client to request resources/updated notifications from the server whenever a particular resource changes.\n */\nconst SubscribeRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"resources/subscribe\"),\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * The URI of the resource to subscribe to. The URI can use any protocol; it is up to the server how to interpret it.\n         */\n        uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    }),\n});\n/**\n * Sent from the client to request cancellation of resources/updated notifications from the server. This should follow a previous resources/subscribe request.\n */\nconst UnsubscribeRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"resources/unsubscribe\"),\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * The URI of the resource to unsubscribe from.\n         */\n        uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    }),\n});\n/**\n * A notification from the server to the client, informing it that a resource has changed and may need to be read again. This should only be sent if the client previously sent a resources/subscribe request.\n */\nconst ResourceUpdatedNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"notifications/resources/updated\"),\n    params: BaseNotificationParamsSchema.extend({\n        /**\n         * The URI of the resource that has been updated. This might be a sub-resource of the one that the client actually subscribed to.\n         */\n        uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    }),\n});\n/* Prompts */\n/**\n * Describes an argument that a prompt can accept.\n */\nconst PromptArgumentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * The name of the argument.\n     */\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    /**\n     * A human-readable description of the argument.\n     */\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n    /**\n     * Whether this argument must be provided.\n     */\n    required: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()),\n})\n    .passthrough();\n/**\n * A prompt or prompt template that the server offers.\n */\nconst PromptSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * The name of the prompt or prompt template.\n     */\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    /**\n     * An optional description of what this prompt provides\n     */\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n    /**\n     * A list of arguments to use for templating the prompt.\n     */\n    arguments: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.array(PromptArgumentSchema)),\n})\n    .passthrough();\n/**\n * Sent from the client to request a list of prompts and prompt templates the server has.\n */\nconst ListPromptsRequestSchema = PaginatedRequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"prompts/list\"),\n});\n/**\n * The server's response to a prompts/list request from the client.\n */\nconst ListPromptsResultSchema = PaginatedResultSchema.extend({\n    prompts: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(PromptSchema),\n});\n/**\n * Used by the client to get a prompt provided by the server.\n */\nconst GetPromptRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"prompts/get\"),\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * The name of the prompt or prompt template.\n         */\n        name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n        /**\n         * Arguments to use for templating the prompt.\n         */\n        arguments: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.record(zod__WEBPACK_IMPORTED_MODULE_0__.z.string())),\n    }),\n});\n/**\n * Text provided to or from an LLM.\n */\nconst TextContentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"text\"),\n    /**\n     * The text content of the message.\n     */\n    text: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n})\n    .passthrough();\n/**\n * An image provided to or from an LLM.\n */\nconst ImageContentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"image\"),\n    /**\n     * The base64-encoded image data.\n     */\n    data: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().base64(),\n    /**\n     * The MIME type of the image. Different providers may support different image types.\n     */\n    mimeType: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n})\n    .passthrough();\n/**\n * An Audio provided to or from an LLM.\n */\nconst AudioContentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"audio\"),\n    /**\n     * The base64-encoded audio data.\n     */\n    data: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().base64(),\n    /**\n     * The MIME type of the audio. Different providers may support different audio types.\n     */\n    mimeType: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n})\n    .passthrough();\n/**\n * The contents of a resource, embedded into a prompt or tool call result.\n */\nconst EmbeddedResourceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"resource\"),\n    resource: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([TextResourceContentsSchema, BlobResourceContentsSchema]),\n})\n    .passthrough();\n/**\n * Describes a message returned as part of a prompt.\n */\nconst PromptMessageSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    role: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\"user\", \"assistant\"]),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n        TextContentSchema,\n        ImageContentSchema,\n        AudioContentSchema,\n        EmbeddedResourceSchema,\n    ]),\n})\n    .passthrough();\n/**\n * The server's response to a prompts/get request from the client.\n */\nconst GetPromptResultSchema = ResultSchema.extend({\n    /**\n     * An optional description for the prompt.\n     */\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n    messages: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(PromptMessageSchema),\n});\n/**\n * An optional notification from the server to the client, informing it that the list of prompts it offers has changed. This may be issued by servers without any previous subscription from the client.\n */\nconst PromptListChangedNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"notifications/prompts/list_changed\"),\n});\n/* Tools */\n/**\n * Additional properties describing a Tool to clients.\n *\n * NOTE: all properties in ToolAnnotations are **hints**.\n * They are not guaranteed to provide a faithful description of\n * tool behavior (including descriptive properties like `title`).\n *\n * Clients should never make tool use decisions based on ToolAnnotations\n * received from untrusted servers.\n */\nconst ToolAnnotationsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * A human-readable title for the tool.\n     */\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n    /**\n     * If true, the tool does not modify its environment.\n     *\n     * Default: false\n     */\n    readOnlyHint: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()),\n    /**\n     * If true, the tool may perform destructive updates to its environment.\n     * If false, the tool performs only additive updates.\n     *\n     * (This property is meaningful only when `readOnlyHint == false`)\n     *\n     * Default: true\n     */\n    destructiveHint: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()),\n    /**\n     * If true, calling the tool repeatedly with the same arguments\n     * will have no additional effect on the its environment.\n     *\n     * (This property is meaningful only when `readOnlyHint == false`)\n     *\n     * Default: false\n     */\n    idempotentHint: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()),\n    /**\n     * If true, this tool may interact with an \"open world\" of external\n     * entities. If false, the tool's domain of interaction is closed.\n     * For example, the world of a web search tool is open, whereas that\n     * of a memory tool is not.\n     *\n     * Default: true\n     */\n    openWorldHint: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()),\n})\n    .passthrough();\n/**\n * Definition for a tool the client can call.\n */\nconst ToolSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * The name of the tool.\n     */\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    /**\n     * A human-readable description of the tool.\n     */\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n    /**\n     * A JSON Schema object defining the expected parameters for the tool.\n     */\n    inputSchema: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"object\"),\n        properties: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({}).passthrough()),\n        required: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string())),\n    })\n        .passthrough(),\n    /**\n     * An optional JSON Schema object defining the structure of the tool's output returned in\n     * the structuredContent field of a CallToolResult.\n     */\n    outputSchema: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        type: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"object\"),\n        properties: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({}).passthrough()),\n        required: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string())),\n    })\n        .passthrough()),\n    /**\n     * Optional additional tool information.\n     */\n    annotations: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(ToolAnnotationsSchema),\n})\n    .passthrough();\n/**\n * Sent from the client to request a list of tools the server has.\n */\nconst ListToolsRequestSchema = PaginatedRequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"tools/list\"),\n});\n/**\n * The server's response to a tools/list request from the client.\n */\nconst ListToolsResultSchema = PaginatedResultSchema.extend({\n    tools: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(ToolSchema),\n});\n/**\n * The server's response to a tool call.\n */\nconst CallToolResultSchema = ResultSchema.extend({\n    /**\n     * A list of content objects that represent the result of the tool call.\n     *\n     * If the Tool does not define an outputSchema, this field MUST be present in the result.\n     * For backwards compatibility, this field is always present, but it may be empty.\n     */\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n        TextContentSchema,\n        ImageContentSchema,\n        AudioContentSchema,\n        EmbeddedResourceSchema,\n    ])).default([]),\n    /**\n     * An object containing structured tool output.\n     *\n     * If the Tool defines an outputSchema, this field MUST be present in the result, and contain a JSON object that matches the schema.\n     */\n    structuredContent: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({}).passthrough().optional(),\n    /**\n     * Whether the tool call ended in an error.\n     *\n     * If not set, this is assumed to be false (the call was successful).\n     *\n     * Any errors that originate from the tool SHOULD be reported inside the result\n     * object, with `isError` set to true, _not_ as an MCP protocol-level error\n     * response. Otherwise, the LLM would not be able to see that an error occurred\n     * and self-correct.\n     *\n     * However, any errors in _finding_ the tool, an error indicating that the\n     * server does not support tool calls, or any other exceptional conditions,\n     * should be reported as an MCP error response.\n     */\n    isError: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()),\n});\n/**\n * CallToolResultSchema extended with backwards compatibility to protocol version 2024-10-07.\n */\nconst CompatibilityCallToolResultSchema = CallToolResultSchema.or(ResultSchema.extend({\n    toolResult: zod__WEBPACK_IMPORTED_MODULE_0__.z.unknown(),\n}));\n/**\n * Used by the client to invoke a tool provided by the server.\n */\nconst CallToolRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"tools/call\"),\n    params: BaseRequestParamsSchema.extend({\n        name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n        arguments: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.record(zod__WEBPACK_IMPORTED_MODULE_0__.z.unknown())),\n    }),\n});\n/**\n * An optional notification from the server to the client, informing it that the list of tools it offers has changed. This may be issued by servers without any previous subscription from the client.\n */\nconst ToolListChangedNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"notifications/tools/list_changed\"),\n});\n/* Logging */\n/**\n * The severity of a log message.\n */\nconst LoggingLevelSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n    \"debug\",\n    \"info\",\n    \"notice\",\n    \"warning\",\n    \"error\",\n    \"critical\",\n    \"alert\",\n    \"emergency\",\n]);\n/**\n * A request from the client to the server, to enable or adjust logging.\n */\nconst SetLevelRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"logging/setLevel\"),\n    params: BaseRequestParamsSchema.extend({\n        /**\n         * The level of logging that the client wants to receive from the server. The server should send all logs at this level and higher (i.e., more severe) to the client as notifications/logging/message.\n         */\n        level: LoggingLevelSchema,\n    }),\n});\n/**\n * Notification of a log message passed from server to client. If no logging/setLevel request has been sent from the client, the server MAY decide which messages to send automatically.\n */\nconst LoggingMessageNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"notifications/message\"),\n    params: BaseNotificationParamsSchema.extend({\n        /**\n         * The severity of this log message.\n         */\n        level: LoggingLevelSchema,\n        /**\n         * An optional name of the logger issuing this message.\n         */\n        logger: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n        /**\n         * The data to be logged, such as a string message or an object. Any JSON serializable type is allowed here.\n         */\n        data: zod__WEBPACK_IMPORTED_MODULE_0__.z.unknown(),\n    }),\n});\n/* Sampling */\n/**\n * Hints to use for model selection.\n */\nconst ModelHintSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * A hint for a model name.\n     */\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n})\n    .passthrough();\n/**\n * The server's preferences for model selection, requested of the client during sampling.\n */\nconst ModelPreferencesSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * Optional hints to use for model selection.\n     */\n    hints: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.array(ModelHintSchema)),\n    /**\n     * How much to prioritize cost when selecting a model.\n     */\n    costPriority: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0).max(1)),\n    /**\n     * How much to prioritize sampling speed (latency) when selecting a model.\n     */\n    speedPriority: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0).max(1)),\n    /**\n     * How much to prioritize intelligence and capabilities when selecting a model.\n     */\n    intelligencePriority: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.number().min(0).max(1)),\n})\n    .passthrough();\n/**\n * Describes a message issued to or received from an LLM API.\n */\nconst SamplingMessageSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    role: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\"user\", \"assistant\"]),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([TextContentSchema, ImageContentSchema, AudioContentSchema]),\n})\n    .passthrough();\n/**\n * A request from the server to sample an LLM via the client. The client has full discretion over which model to select. The client should also inform the user before beginning sampling, to allow them to inspect the request (human in the loop) and decide whether to approve it.\n */\nconst CreateMessageRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"sampling/createMessage\"),\n    params: BaseRequestParamsSchema.extend({\n        messages: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(SamplingMessageSchema),\n        /**\n         * An optional system prompt the server wants to use for sampling. The client MAY modify or omit this prompt.\n         */\n        systemPrompt: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n        /**\n         * A request to include context from one or more MCP servers (including the caller), to be attached to the prompt. The client MAY ignore this request.\n         */\n        includeContext: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\"none\", \"thisServer\", \"allServers\"])),\n        temperature: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.number()),\n        /**\n         * The maximum number of tokens to sample, as requested by the server. The client MAY choose to sample fewer tokens than requested.\n         */\n        maxTokens: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int(),\n        stopSequences: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string())),\n        /**\n         * Optional metadata to pass through to the LLM provider. The format of this metadata is provider-specific.\n         */\n        metadata: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.object({}).passthrough()),\n        /**\n         * The server's preferences for which model to select.\n         */\n        modelPreferences: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(ModelPreferencesSchema),\n    }),\n});\n/**\n * The client's response to a sampling/create_message request from the server. The client should inform the user before returning the sampled message, to allow them to inspect the response (human in the loop) and decide whether to allow the server to see it.\n */\nconst CreateMessageResultSchema = ResultSchema.extend({\n    /**\n     * The name of the model that generated the message.\n     */\n    model: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n    /**\n     * The reason why sampling stopped.\n     */\n    stopReason: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\"endTurn\", \"stopSequence\", \"maxTokens\"]).or(zod__WEBPACK_IMPORTED_MODULE_0__.z.string())),\n    role: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\"user\", \"assistant\"]),\n    content: zod__WEBPACK_IMPORTED_MODULE_0__.z.discriminatedUnion(\"type\", [\n        TextContentSchema,\n        ImageContentSchema,\n        AudioContentSchema\n    ]),\n});\n/* Autocomplete */\n/**\n * A reference to a resource or resource template definition.\n */\nconst ResourceReferenceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"ref/resource\"),\n    /**\n     * The URI or URI template of the resource.\n     */\n    uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n})\n    .passthrough();\n/**\n * Identifies a prompt.\n */\nconst PromptReferenceSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"ref/prompt\"),\n    /**\n     * The name of the prompt or prompt template\n     */\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n})\n    .passthrough();\n/**\n * A request from the client to the server, to ask for completion options.\n */\nconst CompleteRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"completion/complete\"),\n    params: BaseRequestParamsSchema.extend({\n        ref: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([PromptReferenceSchema, ResourceReferenceSchema]),\n        /**\n         * The argument's information\n         */\n        argument: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n            /**\n             * The name of the argument\n             */\n            name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n            /**\n             * The value of the argument to use for completion matching.\n             */\n            value: zod__WEBPACK_IMPORTED_MODULE_0__.z.string(),\n        })\n            .passthrough(),\n    }),\n});\n/**\n * The server's response to a completion/complete request\n */\nconst CompleteResultSchema = ResultSchema.extend({\n    completion: zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n        /**\n         * An array of completion values. Must not exceed 100 items.\n         */\n        values: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()).max(100),\n        /**\n         * The total number of completion options available. This can exceed the number of values actually sent in the response.\n         */\n        total: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.number().int()),\n        /**\n         * Indicates whether there are additional completion options beyond those provided in the current response, even if the exact total is unknown.\n         */\n        hasMore: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.boolean()),\n    })\n        .passthrough(),\n});\n/* Roots */\n/**\n * Represents a root directory or file that the server can operate on.\n */\nconst RootSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    /**\n     * The URI identifying the root. This *must* start with file:// for now.\n     */\n    uri: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().startsWith(\"file://\"),\n    /**\n     * An optional name for the root.\n     */\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.optional(zod__WEBPACK_IMPORTED_MODULE_0__.z.string()),\n})\n    .passthrough();\n/**\n * Sent from the server to request a list of root URIs from the client.\n */\nconst ListRootsRequestSchema = RequestSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"roots/list\"),\n});\n/**\n * The client's response to a roots/list request from the server.\n */\nconst ListRootsResultSchema = ResultSchema.extend({\n    roots: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(RootSchema),\n});\n/**\n * A notification from the client to the server, informing it that the list of roots has changed.\n */\nconst RootsListChangedNotificationSchema = NotificationSchema.extend({\n    method: zod__WEBPACK_IMPORTED_MODULE_0__.z.literal(\"notifications/roots/list_changed\"),\n});\n/* Client messages */\nconst ClientRequestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    PingRequestSchema,\n    InitializeRequestSchema,\n    CompleteRequestSchema,\n    SetLevelRequestSchema,\n    GetPromptRequestSchema,\n    ListPromptsRequestSchema,\n    ListResourcesRequestSchema,\n    ListResourceTemplatesRequestSchema,\n    ReadResourceRequestSchema,\n    SubscribeRequestSchema,\n    UnsubscribeRequestSchema,\n    CallToolRequestSchema,\n    ListToolsRequestSchema,\n]);\nconst ClientNotificationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    CancelledNotificationSchema,\n    ProgressNotificationSchema,\n    InitializedNotificationSchema,\n    RootsListChangedNotificationSchema,\n]);\nconst ClientResultSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    EmptyResultSchema,\n    CreateMessageResultSchema,\n    ListRootsResultSchema,\n]);\n/* Server messages */\nconst ServerRequestSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    PingRequestSchema,\n    CreateMessageRequestSchema,\n    ListRootsRequestSchema,\n]);\nconst ServerNotificationSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    CancelledNotificationSchema,\n    ProgressNotificationSchema,\n    LoggingMessageNotificationSchema,\n    ResourceUpdatedNotificationSchema,\n    ResourceListChangedNotificationSchema,\n    ToolListChangedNotificationSchema,\n    PromptListChangedNotificationSchema,\n]);\nconst ServerResultSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n    EmptyResultSchema,\n    InitializeResultSchema,\n    CompleteResultSchema,\n    GetPromptResultSchema,\n    ListPromptsResultSchema,\n    ListResourcesResultSchema,\n    ListResourceTemplatesResultSchema,\n    ReadResourceResultSchema,\n    CallToolResultSchema,\n    ListToolsResultSchema,\n]);\nclass McpError extends Error {\n    constructor(code, message, data) {\n        super(`MCP error ${code}: ${message}`);\n        this.code = code;\n        this.data = data;\n        this.name = \"McpError\";\n    }\n}\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@modelcontextprotocol/sdk/dist/esm/types.js\n");

/***/ })

};
;