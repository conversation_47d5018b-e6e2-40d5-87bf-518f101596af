import { db } from './connection';
import { messageOperations } from './messages';
import { mcpToolCallOperations } from './mcp-tool-calls';

/**
 * 测试消息排序逻辑的脚本
 */
export function testMessageSorting() {
  console.log('🧪 开始测试消息排序逻辑...');
  
  try {
    // 1. 测试数据库查询排序
    console.log('\n1. 测试数据库查询排序');
    
    // 获取一个测试对话的消息
    const conversations = db.prepare('SELECT id FROM conversations LIMIT 1').all() as Array<{id: number}>;
    
    if (conversations.length === 0) {
      console.log('❌ 没有找到测试对话，请先创建一些对话');
      return;
    }
    
    const testConversationId = conversations[0].id;
    console.log(`使用对话 ID: ${testConversationId}`);
    
    // 从数据库获取消息
    const dbMessages = messageOperations.getByConversationId(testConversationId);
    console.log(`数据库查询到 ${dbMessages.length} 条消息`);
    
    if (dbMessages.length > 0) {
      console.log('数据库排序结果（前5条）:');
      dbMessages.slice(0, 5).forEach((msg, index) => {
        console.log(`  ${index + 1}. ID:${msg.id} Role:${msg.role} Timestamp:${msg.timestamp} Created:${msg.created_at} Seq:${msg.sequence_number}`);
      });
      
      // 验证排序是否正确
      let sortingCorrect = true;
      for (let i = 1; i < dbMessages.length; i++) {
        const prev = dbMessages[i - 1];
        const curr = dbMessages[i];
        
        const prevTimestamp = prev.timestamp || new Date(prev.created_at).getTime();
        const currTimestamp = curr.timestamp || new Date(curr.created_at).getTime();
        
        if (prevTimestamp > currTimestamp) {
          sortingCorrect = false;
          console.log(`❌ 排序错误: 消息 ${prev.id} (${prevTimestamp}) 应该在消息 ${curr.id} (${currTimestamp}) 之后`);
          break;
        } else if (prevTimestamp === currTimestamp && (prev.sequence_number || 0) > (curr.sequence_number || 0)) {
          sortingCorrect = false;
          console.log(`❌ 序列号排序错误: 消息 ${prev.id} (seq:${prev.sequence_number}) 应该在消息 ${curr.id} (seq:${curr.sequence_number}) 之后`);
          break;
        }
      }
      
      if (sortingCorrect) {
        console.log('✅ 数据库排序正确');
      }
    }
    
    // 2. 测试工具调用记录排序
    console.log('\n2. 测试工具调用记录排序');
    
    const toolCalls = mcpToolCallOperations.getByConversationId(testConversationId);
    console.log(`查询到 ${toolCalls.length} 条工具调用记录`);
    
    if (toolCalls.length > 0) {
      console.log('工具调用排序结果:');
      toolCalls.forEach((call, index) => {
        console.log(`  ${index + 1}. ID:${call.id} Tool:${call.tool_name} Created:${call.created_at} Status:${call.status}`);
      });
      
      // 验证工具调用排序
      let toolSortingCorrect = true;
      for (let i = 1; i < toolCalls.length; i++) {
        const prev = new Date(toolCalls[i - 1].created_at).getTime();
        const curr = new Date(toolCalls[i].created_at).getTime();
        
        if (prev > curr) {
          toolSortingCorrect = false;
          console.log(`❌ 工具调用排序错误: ${toolCalls[i - 1].id} 应该在 ${toolCalls[i].id} 之后`);
          break;
        }
      }
      
      if (toolSortingCorrect) {
        console.log('✅ 工具调用排序正确');
      }
    }
    
    // 3. 测试时间戳一致性
    console.log('\n3. 测试时间戳一致性');
    
    const messagesWithoutTimestamp = db.prepare(`
      SELECT COUNT(*) as count 
      FROM messages 
      WHERE timestamp IS NULL
    `).get() as { count: number };
    
    if (messagesWithoutTimestamp.count === 0) {
      console.log('✅ 所有消息都有有效的timestamp');
    } else {
      console.log(`❌ 仍有 ${messagesWithoutTimestamp.count} 条消息没有timestamp`);
    }
    
    // 4. 测试前端排序逻辑模拟
    console.log('\n4. 模拟前端排序逻辑');
    
    if (dbMessages.length > 0) {
      // 模拟前端的排序逻辑
      const frontendSortedMessages = [...dbMessages].sort((a, b) => {
        const aTimestamp = a.timestamp || new Date(a.created_at).getTime();
        const bTimestamp = b.timestamp || new Date(b.created_at).getTime();
        
        if (aTimestamp !== bTimestamp) {
          return aTimestamp - bTimestamp;
        }
        return (a.sequence_number || 0) - (b.sequence_number || 0);
      });
      
      // 比较数据库排序和前端排序是否一致
      let consistencyCheck = true;
      for (let i = 0; i < dbMessages.length; i++) {
        if (dbMessages[i].id !== frontendSortedMessages[i].id) {
          consistencyCheck = false;
          console.log(`❌ 排序不一致: 位置 ${i}, 数据库:${dbMessages[i].id}, 前端:${frontendSortedMessages[i].id}`);
          break;
        }
      }
      
      if (consistencyCheck) {
        console.log('✅ 数据库排序与前端排序一致');
      }
    }
    
    console.log('\n🎉 消息排序测试完成');
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

/**
 * 创建测试数据
 */
export function createTestData() {
  console.log('🔧 创建测试数据...');
  
  try {
    // 创建测试对话
    const conversationResult = db.prepare(`
      INSERT INTO conversations (title, model) 
      VALUES ('测试排序对话', 'llama3.2:latest')
    `).run();
    
    const conversationId = conversationResult.lastInsertRowid as number;
    console.log(`创建测试对话 ID: ${conversationId}`);
    
    // 创建测试消息（故意打乱时间顺序）
    const testMessages = [
      { role: 'user', content: '第一条用户消息', delay: 0 },
      { role: 'assistant', content: '第一条助手回复', delay: 1000 },
      { role: 'user', content: '第二条用户消息', delay: 2000 },
      { role: 'assistant', content: '第二条助手回复', delay: 3000 },
      { role: 'user', content: '第三条用户消息', delay: 4000 },
    ];
    
    const baseTimestamp = Date.now();
    
    testMessages.forEach((msg, index) => {
      messageOperations.create({
        conversation_id: conversationId,
        role: msg.role as 'user' | 'assistant',
        content: msg.content,
        sequence_number: index + 1
      });
    });
    
    console.log(`创建了 ${testMessages.length} 条测试消息`);
    console.log('✅ 测试数据创建完成');
    
    return conversationId;
    
  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
    throw error;
  }
}
