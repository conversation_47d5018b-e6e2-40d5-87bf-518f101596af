"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eventsource-parser";
exports.ids = ["vendor-chunks/eventsource-parser"];
exports.modules = {

/***/ "(rsc)/./node_modules/eventsource-parser/dist/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/eventsource-parser/dist/index.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParseError: () => (/* binding */ ParseError),\n/* harmony export */   createParser: () => (/* binding */ createParser)\n/* harmony export */ });\nclass ParseError extends Error {\n  constructor(message, options) {\n    super(message), this.name = \"ParseError\", this.type = options.type, this.field = options.field, this.value = options.value, this.line = options.line;\n  }\n}\nfunction noop(_arg) {\n}\nfunction createParser(callbacks) {\n  if (typeof callbacks == \"function\")\n    throw new TypeError(\n      \"`callbacks` must be an object, got a function instead. Did you mean `{onEvent: fn}`?\"\n    );\n  const { onEvent = noop, onError = noop, onRetry = noop, onComment } = callbacks;\n  let incompleteLine = \"\", isFirstChunk = !0, id, data = \"\", eventType = \"\";\n  function feed(newChunk) {\n    const chunk = isFirstChunk ? newChunk.replace(/^\\xEF\\xBB\\xBF/, \"\") : newChunk, [complete, incomplete] = splitLines(`${incompleteLine}${chunk}`);\n    for (const line of complete)\n      parseLine(line);\n    incompleteLine = incomplete, isFirstChunk = !1;\n  }\n  function parseLine(line) {\n    if (line === \"\") {\n      dispatchEvent();\n      return;\n    }\n    if (line.startsWith(\":\")) {\n      onComment && onComment(line.slice(line.startsWith(\": \") ? 2 : 1));\n      return;\n    }\n    const fieldSeparatorIndex = line.indexOf(\":\");\n    if (fieldSeparatorIndex !== -1) {\n      const field = line.slice(0, fieldSeparatorIndex), offset = line[fieldSeparatorIndex + 1] === \" \" ? 2 : 1, value = line.slice(fieldSeparatorIndex + offset);\n      processField(field, value, line);\n      return;\n    }\n    processField(line, \"\", line);\n  }\n  function processField(field, value, line) {\n    switch (field) {\n      case \"event\":\n        eventType = value;\n        break;\n      case \"data\":\n        data = `${data}${value}\n`;\n        break;\n      case \"id\":\n        id = value.includes(\"\\0\") ? void 0 : value;\n        break;\n      case \"retry\":\n        /^\\d+$/.test(value) ? onRetry(parseInt(value, 10)) : onError(\n          new ParseError(`Invalid \\`retry\\` value: \"${value}\"`, {\n            type: \"invalid-retry\",\n            value,\n            line\n          })\n        );\n        break;\n      default:\n        onError(\n          new ParseError(\n            `Unknown field \"${field.length > 20 ? `${field.slice(0, 20)}\\u2026` : field}\"`,\n            { type: \"unknown-field\", field, value, line }\n          )\n        );\n        break;\n    }\n  }\n  function dispatchEvent() {\n    data.length > 0 && onEvent({\n      id,\n      event: eventType || void 0,\n      // If the data buffer's last character is a U+000A LINE FEED (LF) character,\n      // then remove the last character from the data buffer.\n      data: data.endsWith(`\n`) ? data.slice(0, -1) : data\n    }), id = void 0, data = \"\", eventType = \"\";\n  }\n  function reset(options = {}) {\n    incompleteLine && options.consume && parseLine(incompleteLine), isFirstChunk = !0, id = void 0, data = \"\", eventType = \"\", incompleteLine = \"\";\n  }\n  return { feed, reset };\n}\nfunction splitLines(chunk) {\n  const lines = [];\n  let incompleteLine = \"\", searchIndex = 0;\n  for (; searchIndex < chunk.length; ) {\n    const crIndex = chunk.indexOf(\"\\r\", searchIndex), lfIndex = chunk.indexOf(`\n`, searchIndex);\n    let lineEnd = -1;\n    if (crIndex !== -1 && lfIndex !== -1 ? lineEnd = Math.min(crIndex, lfIndex) : crIndex !== -1 ? lineEnd = crIndex : lfIndex !== -1 && (lineEnd = lfIndex), lineEnd === -1) {\n      incompleteLine = chunk.slice(searchIndex);\n      break;\n    } else {\n      const line = chunk.slice(searchIndex, lineEnd);\n      lines.push(line), searchIndex = lineEnd + 1, chunk[searchIndex - 1] === \"\\r\" && chunk[searchIndex] === `\n` && searchIndex++;\n    }\n  }\n  return [lines, incompleteLine];\n}\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/eventsource-parser/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/eventsource-parser/dist/stream.js":
/*!********************************************************!*\
  !*** ./node_modules/eventsource-parser/dist/stream.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventSourceParserStream: () => (/* binding */ EventSourceParserStream),\n/* harmony export */   ParseError: () => (/* reexport safe */ _index_js__WEBPACK_IMPORTED_MODULE_0__.ParseError)\n/* harmony export */ });\n/* harmony import */ var _index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.js */ \"(rsc)/./node_modules/eventsource-parser/dist/index.js\");\n\n\nclass EventSourceParserStream extends TransformStream {\n  constructor({ onError, onRetry, onComment } = {}) {\n    let parser;\n    super({\n      start(controller) {\n        parser = (0,_index_js__WEBPACK_IMPORTED_MODULE_0__.createParser)({\n          onEvent: (event) => {\n            controller.enqueue(event);\n          },\n          onError(error) {\n            onError === \"terminate\" ? controller.error(error) : typeof onError == \"function\" && onError(error);\n          },\n          onRetry,\n          onComment\n        });\n      },\n      transform(chunk) {\n        parser.feed(chunk);\n      }\n    });\n  }\n}\n\n//# sourceMappingURL=stream.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZXZlbnRzb3VyY2UtcGFyc2VyL2Rpc3Qvc3RyZWFtLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEwQztBQUNGO0FBQ3hDO0FBQ0EsZ0JBQWdCLDhCQUE4QixJQUFJO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQix1REFBWTtBQUM3QjtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFJRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFphY2tcXERlc2t0b3BcXFJQMzBfa3VuYWdlbnRcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXGV2ZW50c291cmNlLXBhcnNlclxcZGlzdFxcc3RyZWFtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZVBhcnNlciB9IGZyb20gXCIuL2luZGV4LmpzXCI7XG5pbXBvcnQgeyBQYXJzZUVycm9yIH0gZnJvbSBcIi4vaW5kZXguanNcIjtcbmNsYXNzIEV2ZW50U291cmNlUGFyc2VyU3RyZWFtIGV4dGVuZHMgVHJhbnNmb3JtU3RyZWFtIHtcbiAgY29uc3RydWN0b3IoeyBvbkVycm9yLCBvblJldHJ5LCBvbkNvbW1lbnQgfSA9IHt9KSB7XG4gICAgbGV0IHBhcnNlcjtcbiAgICBzdXBlcih7XG4gICAgICBzdGFydChjb250cm9sbGVyKSB7XG4gICAgICAgIHBhcnNlciA9IGNyZWF0ZVBhcnNlcih7XG4gICAgICAgICAgb25FdmVudDogKGV2ZW50KSA9PiB7XG4gICAgICAgICAgICBjb250cm9sbGVyLmVucXVldWUoZXZlbnQpO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgb25FcnJvcihlcnJvcikge1xuICAgICAgICAgICAgb25FcnJvciA9PT0gXCJ0ZXJtaW5hdGVcIiA/IGNvbnRyb2xsZXIuZXJyb3IoZXJyb3IpIDogdHlwZW9mIG9uRXJyb3IgPT0gXCJmdW5jdGlvblwiICYmIG9uRXJyb3IoZXJyb3IpO1xuICAgICAgICAgIH0sXG4gICAgICAgICAgb25SZXRyeSxcbiAgICAgICAgICBvbkNvbW1lbnRcbiAgICAgICAgfSk7XG4gICAgICB9LFxuICAgICAgdHJhbnNmb3JtKGNodW5rKSB7XG4gICAgICAgIHBhcnNlci5mZWVkKGNodW5rKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfVxufVxuZXhwb3J0IHtcbiAgRXZlbnRTb3VyY2VQYXJzZXJTdHJlYW0sXG4gIFBhcnNlRXJyb3Jcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdHJlYW0uanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/eventsource-parser/dist/stream.js\n");

/***/ })

};
;