[{"C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\chat\\route.ts": "1", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\conversations\\route.ts": "2", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\conversations\\[id]\\clear\\route.ts": "3", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\conversations\\[id]\\route.ts": "4", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\call-tool\\route.ts": "5", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\config\\route.ts": "6", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\server-list\\route.ts": "7", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\server-status\\route.ts": "8", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\servers\\route.ts": "9", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\servers\\[id]\\connect\\route.ts": "10", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\servers\\[id]\\route.ts": "11", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\status\\route.ts": "12", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\tool-config\\route.ts": "13", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\tools\\route.ts": "14", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\tools\\[id]\\route.ts": "15", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\validate\\route.ts": "16", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\models\\route.ts": "17", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\AIStatusIndicator.tsx": "18", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ChatArea.tsx": "19", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ChatHeader.tsx": "20", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ChatInput.tsx": "21", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ChatLayout.tsx": "22", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ErrorDisplay.tsx": "23", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\index.ts": "24", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\MCPToolCallVisualization.tsx": "25", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\MessageItem.tsx": "26", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\MessageList.tsx": "27", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ToolCallMessage.tsx": "28", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ToolPanel.tsx": "29", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ToolSettings.tsx": "30", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ToolToggle.tsx": "31", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\TypingIndicator.tsx": "32", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\WelcomePage.tsx": "33", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\hooks\\index.ts": "34", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\hooks\\useChat.ts": "35", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\hooks\\useToolSettings.ts": "36", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\page.tsx": "37", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\types.ts": "38", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\layout.tsx": "39", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\AddServerModal.tsx": "40", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\LoadingSpinner.tsx": "41", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\PageHeader.tsx": "42", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\StatsCards.tsx": "43", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\ToolsList.tsx": "44", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\ToolsModal.tsx": "45", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\hooks\\useMcpConfig.ts": "46", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\page.tsx": "47", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\types.ts": "48", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\page.tsx": "49", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\Sidebar.tsx": "50", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\connection.ts": "51", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\conversations.ts": "52", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\index.ts": "53", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\mcp-servers.ts": "54", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\mcp-tool-calls.ts": "55", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\mcp-tools.ts": "56", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\messages.ts": "57", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\migrate-timestamps.ts": "58", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\test-sorting.ts": "59", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\types.ts": "60", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database.ts": "61", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client-server.ts": "62", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client-sse.ts": "63", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client-streamable-http.ts": "64", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client.ts": "65", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-multi-server-client.ts": "66", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-server.ts": "67", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-tools.ts": "68", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\ollama.ts": "69", "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\tools.ts": "70"}, {"size": 27902, "mtime": 1750311195967, "results": "71", "hashOfConfig": "72"}, {"size": 1593, "mtime": 1749563999756, "results": "73", "hashOfConfig": "72"}, {"size": 1207, "mtime": 1750038415091, "results": "74", "hashOfConfig": "72"}, {"size": 4018, "mtime": 1750302980009, "results": "75", "hashOfConfig": "72"}, {"size": 4737, "mtime": 1749954389275, "results": "76", "hashOfConfig": "72"}, {"size": 5272, "mtime": 1749971498866, "results": "77", "hashOfConfig": "72"}, {"size": 4298, "mtime": 1749976290268, "results": "78", "hashOfConfig": "72"}, {"size": 5330, "mtime": 1749990008095, "results": "79", "hashOfConfig": "72"}, {"size": 12609, "mtime": 1749970835157, "results": "80", "hashOfConfig": "72"}, {"size": 6009, "mtime": 1750316267294, "results": "81", "hashOfConfig": "72"}, {"size": 4109, "mtime": 1749724888781, "results": "82", "hashOfConfig": "72"}, {"size": 1276, "mtime": 1749691944366, "results": "83", "hashOfConfig": "72"}, {"size": 3388, "mtime": 1749705855502, "results": "84", "hashOfConfig": "72"}, {"size": 8701, "mtime": 1750041885205, "results": "85", "hashOfConfig": "72"}, {"size": 1650, "mtime": 1749865521897, "results": "86", "hashOfConfig": "72"}, {"size": 5493, "mtime": 1749782782039, "results": "87", "hashOfConfig": "72"}, {"size": 1699, "mtime": 1749564036885, "results": "88", "hashOfConfig": "72"}, {"size": 2710, "mtime": 1749900531146, "results": "89", "hashOfConfig": "72"}, {"size": 2688, "mtime": 1750248107772, "results": "90", "hashOfConfig": "72"}, {"size": 1258, "mtime": 1749869168617, "results": "91", "hashOfConfig": "72"}, {"size": 3963, "mtime": 1750038340814, "results": "92", "hashOfConfig": "72"}, {"size": 3868, "mtime": 1750248145022, "results": "93", "hashOfConfig": "72"}, {"size": 384, "mtime": 1749868822196, "results": "94", "hashOfConfig": "72"}, {"size": 588, "mtime": 1749870863081, "results": "95", "hashOfConfig": "72"}, {"size": 12292, "mtime": 1750247029340, "results": "96", "hashOfConfig": "72"}, {"size": 8385, "mtime": 1750250584921, "results": "97", "hashOfConfig": "72"}, {"size": 4488, "mtime": 1750316038565, "results": "98", "hashOfConfig": "72"}, {"size": 5994, "mtime": 1750303252444, "results": "99", "hashOfConfig": "72"}, {"size": 7517, "mtime": 1750078234398, "results": "100", "hashOfConfig": "72"}, {"size": 1901, "mtime": 1750039881614, "results": "101", "hashOfConfig": "72"}, {"size": 4899, "mtime": 1750078400075, "results": "102", "hashOfConfig": "72"}, {"size": 854, "mtime": 1749869440783, "results": "103", "hashOfConfig": "72"}, {"size": 1710, "mtime": 1749869761928, "results": "104", "hashOfConfig": "72"}, {"size": 89, "mtime": 1749868872056, "results": "105", "hashOfConfig": "72"}, {"size": 23886, "mtime": 1750302580113, "results": "106", "hashOfConfig": "72"}, {"size": 4268, "mtime": 1750078195296, "results": "107", "hashOfConfig": "72"}, {"size": 250, "mtime": 1749868705898, "results": "108", "hashOfConfig": "72"}, {"size": 846, "mtime": 1749875186578, "results": "109", "hashOfConfig": "72"}, {"size": 421, "mtime": 1749613150398, "results": "110", "hashOfConfig": "72"}, {"size": 18307, "mtime": 1749970991719, "results": "111", "hashOfConfig": "72"}, {"size": 417, "mtime": 1749646569882, "results": "112", "hashOfConfig": "72"}, {"size": 1241, "mtime": 1749695594323, "results": "113", "hashOfConfig": "72"}, {"size": 2128, "mtime": 1749695268702, "results": "114", "hashOfConfig": "72"}, {"size": 5407, "mtime": 1749865028754, "results": "115", "hashOfConfig": "72"}, {"size": 16000, "mtime": 1749866482894, "results": "116", "hashOfConfig": "72"}, {"size": 10641, "mtime": 1749992999209, "results": "117", "hashOfConfig": "72"}, {"size": 10745, "mtime": 1749867218359, "results": "118", "hashOfConfig": "72"}, {"size": 2594, "mtime": 1749969651363, "results": "119", "hashOfConfig": "72"}, {"size": 119, "mtime": 1749868891457, "results": "120", "hashOfConfig": "72"}, {"size": 4792, "mtime": 1749870829274, "results": "121", "hashOfConfig": "72"}, {"size": 6922, "mtime": 1750316137828, "results": "122", "hashOfConfig": "72"}, {"size": 1805, "mtime": 1749976751706, "results": "123", "hashOfConfig": "72"}, {"size": 3353, "mtime": 1750314827057, "results": "124", "hashOfConfig": "72"}, {"size": 3863, "mtime": 1749976787277, "results": "125", "hashOfConfig": "72"}, {"size": 3530, "mtime": 1750316051355, "results": "126", "hashOfConfig": "72"}, {"size": 3626, "mtime": 1749976807571, "results": "127", "hashOfConfig": "72"}, {"size": 3045, "mtime": 1750316020055, "results": "128", "hashOfConfig": "72"}, {"size": 3654, "mtime": 1750316096303, "results": "129", "hashOfConfig": "72"}, {"size": 6681, "mtime": 1750316177435, "results": "130", "hashOfConfig": "72"}, {"size": 3687, "mtime": 1750311033049, "results": "131", "hashOfConfig": "72"}, {"size": 312, "mtime": 1749977103967, "results": "132", "hashOfConfig": "72"}, {"size": 5039, "mtime": 1749725998327, "results": "133", "hashOfConfig": "72"}, {"size": 11394, "mtime": 1749779080651, "results": "134", "hashOfConfig": "72"}, {"size": 15167, "mtime": 1749784407894, "results": "135", "hashOfConfig": "72"}, {"size": 4792, "mtime": 1750055875481, "results": "136", "hashOfConfig": "72"}, {"size": 18856, "mtime": 1749971621651, "results": "137", "hashOfConfig": "72"}, {"size": 9457, "mtime": 1750060506753, "results": "138", "hashOfConfig": "72"}, {"size": 6634, "mtime": 1749726048431, "results": "139", "hashOfConfig": "72"}, {"size": 7781, "mtime": 1749878394464, "results": "140", "hashOfConfig": "72"}, {"size": 17986, "mtime": 1750055759029, "results": "141", "hashOfConfig": "72"}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1wlrykr", {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\chat\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\conversations\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\conversations\\[id]\\clear\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\conversations\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\call-tool\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\config\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\server-list\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\server-status\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\servers\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\servers\\[id]\\connect\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\servers\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\status\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\tool-config\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\tools\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\tools\\[id]\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\mcp\\validate\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\api\\models\\route.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\AIStatusIndicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ChatArea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ChatHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ChatInput.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ChatLayout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ErrorDisplay.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\MCPToolCallVisualization.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\MessageItem.tsx", ["352"], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\MessageList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ToolCallMessage.tsx", ["353"], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ToolPanel.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ToolSettings.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\ToolToggle.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\TypingIndicator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\components\\WelcomePage.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\hooks\\useChat.ts", ["354"], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\hooks\\useToolSettings.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\chat\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\AddServerModal.tsx", ["355"], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\PageHeader.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\StatsCards.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\ToolsList.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\components\\ToolsModal.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\hooks\\useMcpConfig.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\mcp-config\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\app\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\connection.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\conversations.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\index.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\mcp-servers.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\mcp-tool-calls.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\mcp-tools.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\messages.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\migrate-timestamps.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\test-sorting.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database\\types.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\database.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client-server.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client-sse.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client-streamable-http.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-multi-server-client.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-server.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\mcp\\mcp-tools.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\ollama.ts", [], [], "C:\\Users\\<USER>\\Desktop\\RP30_kunagent\\frontend\\src\\lib\\tools.ts", [], [], {"ruleId": "356", "severity": 1, "message": "357", "line": 34, "column": 19, "nodeType": "358", "endLine": 42, "endColumn": 21}, {"ruleId": "356", "severity": 1, "message": "357", "line": 66, "column": 17, "nodeType": "358", "endLine": 73, "endColumn": 19}, {"ruleId": "359", "severity": 1, "message": "360", "line": 632, "column": 6, "nodeType": "361", "endLine": 632, "endColumn": 8, "suggestions": "362"}, {"ruleId": "359", "severity": 1, "message": "363", "line": 60, "column": 6, "nodeType": "361", "endLine": 60, "endColumn": 92, "suggestions": "364"}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadModels'. Either include it or remove the dependency array.", "ArrayExpression", ["365"], "React Hook useEffect has missing dependencies: 'cachedServerKey', 'getServerKey', and 'newServer'. Either include them or remove the dependency array.", ["366"], {"desc": "367", "fix": "368"}, {"desc": "369", "fix": "370"}, "Update the dependencies array to be: [loadModels]", {"range": "371", "text": "372"}, "Update the dependencies array to be: [newServer.type, newServer.url, newServer.base_url, newServer.command, newServer.args, getServerKey, newServer, cachedServerKey]", {"range": "373", "text": "374"}, [20745, 20747], "[loadModels]", [1819, 1905], "[newServer.type, newServer.url, newServer.base_url, newServer.command, newServer.args, getServerKey, newServer, cachedServerKey]"]