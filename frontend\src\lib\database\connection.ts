import Database from 'better-sqlite3';
import path from 'path';

// 数据库连接配置
const dbPath = path.join(process.cwd(), 'chat.db');
export const db = new Database(dbPath);

// 数据库初始化SQL
export const initializeDatabase = () => {
  const initSQL = `
    CREATE TABLE IF NOT EXISTS conversations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      model TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );

    CREATE TABLE IF NOT EXISTS messages (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      conversation_id INTEGER NOT NULL,
      role TEXT NOT NULL,
      content TEXT NOT NULL,
      model TEXT,
      sequence_number INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      timestamp INTEGER, -- 毫秒级时间戳，用于精确排序
      -- 工具调用相关字段
      tool_name TEXT, -- 工具名称
      tool_args TEXT, -- 工具参数 (JSON)
      tool_result TEXT, -- 工具结果 (JSON)
      tool_status TEXT CHECK (tool_status IN ('executing', 'completed', 'error')), -- 工具状态
      tool_execution_time INTEGER, -- 工具执行时间(毫秒)
      tool_error TEXT, -- 工具错误信息
      -- Ollama生成统计信息
      total_duration INTEGER,
      load_duration INTEGER,
      prompt_eval_count INTEGER,
      prompt_eval_duration INTEGER,
      eval_count INTEGER,
      eval_duration INTEGER,
      FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
    );

    -- MCP服务器统一配置表
    CREATE TABLE IF NOT EXISTS mcp_servers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      display_name TEXT NOT NULL,
      description TEXT,
      type TEXT NOT NULL CHECK (type IN ('stdio', 'sse', 'streamable-http')),
      status TEXT NOT NULL DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'error', 'connecting')),
      enabled BOOLEAN NOT NULL DEFAULT 1,
      
      -- STDIO配置
      command TEXT,
      args TEXT, -- JSON数组格式
      working_directory TEXT,
      
      -- SSE/HTTP配置
      url TEXT,
      base_url TEXT,
      port INTEGER,
      path TEXT DEFAULT '/',
      protocol TEXT DEFAULT 'http' CHECK (protocol IN ('http', 'https')),
      
      -- 通用配置
      headers TEXT, -- JSON对象格式
      auth_type TEXT CHECK (auth_type IN ('none', 'bearer', 'basic', 'api_key')),
      auth_config TEXT, -- JSON格式
      timeout_ms INTEGER DEFAULT 30000,
      retry_attempts INTEGER DEFAULT 3,
      retry_delay_ms INTEGER DEFAULT 1000,
      
      -- 扩展配置
      extra_config TEXT, -- JSON格式，存储其他特殊配置
      
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      last_connected_at DATETIME,
      error_message TEXT
    );

    -- MCP工具表
    CREATE TABLE IF NOT EXISTS mcp_tools (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      server_id INTEGER NOT NULL,
      name TEXT NOT NULL,
      description TEXT,
      input_schema TEXT, -- JSON格式存储工具的输入参数模式
      is_available BOOLEAN DEFAULT 1,
      enabled BOOLEAN DEFAULT 1, -- 工具是否启用（在对话页面可见）
      last_used_at DATETIME,
      usage_count INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (server_id) REFERENCES mcp_servers (id) ON DELETE CASCADE,
      UNIQUE(server_id, name)
    );

    -- MCP工具调用记录表
    CREATE TABLE IF NOT EXISTS mcp_tool_calls (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      tool_id INTEGER NOT NULL,
      conversation_id INTEGER NOT NULL, -- 关联到对话
      message_id INTEGER, -- 关联到具体的消息（可选）
      input_args TEXT, -- JSON格式存储输入参数
      output_result TEXT, -- JSON格式存储输出结果
      execution_time_ms INTEGER,
      status TEXT NOT NULL CHECK (status IN ('success', 'error', 'timeout')),
      error_message TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (tool_id) REFERENCES mcp_tools (id) ON DELETE CASCADE,
      FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE,
      FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE SET NULL
    );

    -- 原有索引
    CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
    CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations(updated_at DESC);
    
    -- MCP相关索引
    CREATE INDEX IF NOT EXISTS idx_mcp_servers_type ON mcp_servers(type);
    CREATE INDEX IF NOT EXISTS idx_mcp_servers_status ON mcp_servers(status);
    CREATE INDEX IF NOT EXISTS idx_mcp_servers_enabled ON mcp_servers(enabled);
    CREATE INDEX IF NOT EXISTS idx_mcp_tools_server_id ON mcp_tools(server_id);
    CREATE INDEX IF NOT EXISTS idx_mcp_tools_name ON mcp_tools(name);
    CREATE INDEX IF NOT EXISTS idx_mcp_tools_available ON mcp_tools(is_available);
    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_conversation_id ON mcp_tool_calls(conversation_id);
    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_tool_id ON mcp_tool_calls(tool_id);
    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_message_id ON mcp_tool_calls(message_id);
    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_created_at ON mcp_tool_calls(created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_status ON mcp_tool_calls(status);
    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_conv_time ON mcp_tool_calls(conversation_id, created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_conv_status ON mcp_tool_calls(conversation_id, status);
  `;

  // 执行初始化SQL
  db.exec(initSQL);

  // 数据库迁移：为messages表添加timestamp列（如果不存在）
  try {
    // 检查timestamp列是否存在
    const columns = db.prepare("PRAGMA table_info(messages)").all() as Array<{name: string}>;
    const hasTimestamp = columns.some(col => col.name === 'timestamp');
    
    if (!hasTimestamp) {
      console.log('正在为messages表添加timestamp列...');
      db.exec('ALTER TABLE messages ADD COLUMN timestamp INTEGER');
      
      // 为现有消息设置timestamp（基于created_at）
      const updateTimestamp = db.prepare(`
        UPDATE messages 
        SET timestamp = CAST((julianday(created_at) - 2440587.5) * 86400000 AS INTEGER)
        WHERE timestamp IS NULL
      `);
      updateTimestamp.run();
      console.log('messages表timestamp列添加完成');
    }
  } catch (error) {
    console.error('数据库迁移失败:', error);
  }

  console.log('数据库初始化完成');

  // 运行时间戳迁移（如果需要）
  try {
    const { autoMigrateIfNeeded } = require('./migrate-timestamps');
    autoMigrateIfNeeded();
  } catch (error) {
    console.error('时间戳迁移失败:', error);
  }
};

// 使用全局变量避免重复初始化
declare global {
  var __db_initialized: boolean | undefined;
}

if (!global.__db_initialized) {
  initializeDatabase();
  global.__db_initialized = true;
}

export default db;