import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const dbPath = path.join(__dirname, 'src', 'lib', 'database', 'chat.db');
console.log('数据库路径:', dbPath);

try {
  const db = new Database(dbPath);
  
  // 查询最近的消息，包括工具调用消息
  const messages = db.prepare(`
    SELECT id, conversation_id, role, content, tool_name, tool_args, tool_result, tool_status, tool_execution_time, tool_error, created_at
    FROM messages 
    ORDER BY id DESC 
    LIMIT 10
  `).all();
  
  console.log('最近的10条消息:');
  messages.forEach(msg => {
    console.log(`ID: ${msg.id}, Role: ${msg.role}, Tool: ${msg.tool_name || 'N/A'}, Status: ${msg.tool_status || 'N/A'}`);
    if (msg.role === 'tool_call') {
      console.log(`  工具名称: ${msg.tool_name}`);
      console.log(`  工具参数: ${msg.tool_args}`);
      console.log(`  工具状态: ${msg.tool_status}`);
      console.log(`  执行时间: ${msg.tool_execution_time}ms`);
      if (msg.tool_result) {
        console.log(`  工具结果: ${msg.tool_result.substring(0, 100)}...`);
      }
      if (msg.tool_error) {
        console.log(`  错误信息: ${msg.tool_error}`);
      }
    }
    console.log('---');
  });
  
  // 检查是否有tool_call类型的消息
  const toolCallCount = db.prepare(`
    SELECT COUNT(*) as count FROM messages WHERE role = 'tool_call'
  `).get();
  
  console.log(`\n数据库中tool_call类型消息总数: ${toolCallCount.count}`);
  
  db.close();
} catch (error) {
  console.error('数据库查询失败:', error);
}
