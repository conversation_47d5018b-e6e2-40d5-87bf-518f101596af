import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const dbPath = path.join(__dirname, 'chat.db');
console.log('数据库路径:', dbPath);

try {
  const db = new Database(dbPath);

  // 首先检查表是否存在
  const tables = db.prepare(`
    SELECT name FROM sqlite_master WHERE type='table'
  `).all();

  console.log('数据库中的表:', tables.map(t => t.name));

  // 如果messages表不存在，先创建
  if (!tables.some(t => t.name === 'messages')) {
    console.log('messages表不存在，正在创建...');
    // 这里应该调用数据库初始化
    db.close();
    console.log('请先运行应用程序以初始化数据库');
    process.exit(1);
  }

  // 检查messages表的结构
  const columns = db.prepare(`PRAGMA table_info(messages)`).all();
  console.log('messages表的列:', columns.map(c => c.name));

  // 检查是否缺少工具调用相关的列
  const requiredColumns = ['tool_name', 'tool_args', 'tool_result', 'tool_status', 'tool_execution_time', 'tool_error'];
  const existingColumns = columns.map(c => c.name);
  const missingColumns = requiredColumns.filter(col => !existingColumns.includes(col));

  if (missingColumns.length > 0) {
    console.log('缺少的列:', missingColumns);
    console.log('正在添加缺少的列...');

    // 添加缺少的列
    for (const column of missingColumns) {
      try {
        let columnDef = '';
        switch (column) {
          case 'tool_name':
            columnDef = 'TEXT';
            break;
          case 'tool_args':
          case 'tool_result':
          case 'tool_error':
            columnDef = 'TEXT';
            break;
          case 'tool_status':
            columnDef = "TEXT CHECK (tool_status IN ('executing', 'completed', 'error'))";
            break;
          case 'tool_execution_time':
            columnDef = 'INTEGER';
            break;
        }

        const sql = `ALTER TABLE messages ADD COLUMN ${column} ${columnDef}`;
        console.log(`执行: ${sql}`);
        db.exec(sql);
        console.log(`成功添加列: ${column}`);
      } catch (error) {
        console.error(`添加列 ${column} 失败:`, error);
      }
    }

    // 更新role列的约束以包含tool_call
    try {
      console.log('正在更新role列约束...');
      // SQLite不支持直接修改CHECK约束，需要重建表
      // 但为了简单起见，我们先跳过这一步，因为SQLite通常不会严格执行CHECK约束
      console.log('role列约束更新跳过（SQLite限制）');
    } catch (error) {
      console.error('更新role列约束失败:', error);
    }
  } else {
    console.log('所有必需的列都存在');
  }

  // 查询最近的消息，包括工具调用消息
  const messages = db.prepare(`
    SELECT id, conversation_id, role, content, created_at
    FROM messages
    ORDER BY id DESC
    LIMIT 10
  `).all();
  
  console.log('最近的10条消息:');
  messages.forEach(msg => {
    console.log(`ID: ${msg.id}, Role: ${msg.role}, Content: ${msg.content.substring(0, 50)}...`);
    console.log('---');
  });
  
  // 检查是否有tool_call类型的消息
  const toolCallCount = db.prepare(`
    SELECT COUNT(*) as count FROM messages WHERE role = 'tool_call'
  `).get();

  console.log(`\n数据库中tool_call类型消息总数: ${toolCallCount.count}`);

  // 检查MCP服务器配置
  const mcpServers = db.prepare(`
    SELECT name, display_name, status, enabled FROM mcp_servers
  `).all();

  console.log('\nMCP服务器配置:');
  mcpServers.forEach(server => {
    console.log(`  ${server.name}: ${server.display_name} (状态: ${server.status}, 启用: ${server.enabled})`);
  });

  // 检查可用的MCP工具
  const mcpTools = db.prepare(`
    SELECT t.name, t.description, s.name as server_name, t.enabled
    FROM mcp_tools t
    JOIN mcp_servers s ON t.server_id = s.id
    WHERE t.is_available = 1
  `).all();

  console.log('\n可用的MCP工具:');
  mcpTools.forEach(tool => {
    console.log(`  ${tool.name} (${tool.server_name}): ${tool.description || '无描述'} (启用: ${tool.enabled})`);
  });
  
  db.close();
} catch (error) {
  console.error('数据库查询失败:', error);
}
