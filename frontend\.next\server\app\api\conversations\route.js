/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/conversations/route";
exports.ids = ["app/api/conversations/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconversations%2Froute&page=%2Fapi%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconversations%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconversations%2Froute&page=%2Fapi%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconversations%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_conversations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/conversations/route.ts */ \"(rsc)/./src/app/api/conversations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/conversations/route\",\n        pathname: \"/api/conversations\",\n        filename: \"route\",\n        bundlePath: \"app/api/conversations/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\RP30_kunagent\\\\frontend\\\\src\\\\app\\\\api\\\\conversations\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Zack_Desktop_RP30_kunagent_frontend_src_app_api_conversations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconversations%2Froute&page=%2Fapi%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconversations%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/conversations/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/conversations/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_database__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/database */ \"(rsc)/./src/lib/database.ts\");\n\n\n// 获取所有对话\nasync function GET(request) {\n    try {\n        const conversations = _lib_database__WEBPACK_IMPORTED_MODULE_1__.dbOperations.getAllConversations();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            conversations\n        });\n    } catch (error) {\n        console.error('获取对话列表失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: '获取对话列表失败',\n            message: error instanceof Error ? error.message : '未知错误'\n        }, {\n            status: 500\n        });\n    }\n}\n// 创建新对话\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { title, model } = body;\n        // 验证必需参数\n        if (!title || !model) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: '缺少必需参数: title 和 model'\n            }, {\n                status: 400\n            });\n        }\n        // 创建新对话\n        const conversationId = _lib_database__WEBPACK_IMPORTED_MODULE_1__.dbOperations.createConversation({\n            title: title.trim(),\n            model\n        });\n        // 获取创建的对话\n        const conversation = _lib_database__WEBPACK_IMPORTED_MODULE_1__.dbOperations.getConversationById(conversationId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            conversation\n        }, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('创建对话失败:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: '创建对话失败',\n            message: error instanceof Error ? error.message : '未知错误'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/conversations/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conversationOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.conversationOperations),\n/* harmony export */   conversationQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.conversationQueries),\n/* harmony export */   db: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.db),\n/* harmony export */   dbOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.dbOperations),\n/* harmony export */   \"default\": () => (/* reexport safe */ _database_connection__WEBPACK_IMPORTED_MODULE_1__.db),\n/* harmony export */   initializeDatabase: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase),\n/* harmony export */   mcpDbOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpDbOperations),\n/* harmony export */   mcpServerOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpServerOperations),\n/* harmony export */   mcpServerQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpServerQueries),\n/* harmony export */   mcpToolCallOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpToolCallOperations),\n/* harmony export */   mcpToolCallQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpToolCallQueries),\n/* harmony export */   mcpToolOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpToolOperations),\n/* harmony export */   mcpToolQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.mcpToolQueries),\n/* harmony export */   messageOperations: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.messageOperations),\n/* harmony export */   messageQueries: () => (/* reexport safe */ _database_index__WEBPACK_IMPORTED_MODULE_0__.messageQueries)\n/* harmony export */ });\n/* harmony import */ var _database_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./database/index */ \"(rsc)/./src/lib/database/index.ts\");\n/* harmony import */ var _database_connection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./database/connection */ \"(rsc)/./src/lib/database/connection.ts\");\n// 重新导出模块化的数据库操作\n// 这个文件现在作为向后兼容的入口点，所有实际的实现都在 ./database/ 目录下\n\n// 重新导出数据库连接作为默认导出（保持向后兼容性）\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RhdGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsZ0JBQWdCO0FBQ2hCLDZDQUE2QztBQUNaO0FBRWpDLDJCQUEyQjtBQUMyQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxaYWNrXFxEZXNrdG9wXFxSUDMwX2t1bmFnZW50XFxmcm9udGVuZFxcc3JjXFxsaWJcXGRhdGFiYXNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIOmHjeaWsOWvvOWHuuaooeWdl+WMlueahOaVsOaNruW6k+aTjeS9nFxuLy8g6L+Z5Liq5paH5Lu2546w5Zyo5L2c5Li65ZCR5ZCO5YW85a6555qE5YWl5Y+j54K577yM5omA5pyJ5a6e6ZmF55qE5a6e546w6YO95ZyoIC4vZGF0YWJhc2UvIOebruW9leS4i1xuZXhwb3J0ICogZnJvbSAnLi9kYXRhYmFzZS9pbmRleCc7XG5cbi8vIOmHjeaWsOWvvOWHuuaVsOaNruW6k+i/nuaOpeS9nOS4uum7mOiupOWvvOWHuu+8iOS/neaMgeWQkeWQjuWFvOWuueaAp++8iVxuZXhwb3J0IHsgZGIgYXMgZGVmYXVsdCB9IGZyb20gJy4vZGF0YWJhc2UvY29ubmVjdGlvbic7Il0sIm5hbWVzIjpbImRiIiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/connection.ts":
/*!****************************************!*\
  !*** ./src/lib/database/connection.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase)\n/* harmony export */ });\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! better-sqlite3 */ \"better-sqlite3\");\n/* harmony import */ var better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(better_sqlite3__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// 数据库连接配置\nconst dbPath = path__WEBPACK_IMPORTED_MODULE_1___default().join(process.cwd(), 'chat.db');\nconst db = new (better_sqlite3__WEBPACK_IMPORTED_MODULE_0___default())(dbPath);\n// 数据库初始化SQL\nconst initializeDatabase = ()=>{\n    const initSQL = `\n    CREATE TABLE IF NOT EXISTS conversations (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      title TEXT NOT NULL,\n      model TEXT NOT NULL,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP\n    );\n\n    CREATE TABLE IF NOT EXISTS messages (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      conversation_id INTEGER NOT NULL,\n      role TEXT NOT NULL,\n      content TEXT NOT NULL,\n      model TEXT,\n      sequence_number INTEGER DEFAULT 0,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      timestamp INTEGER, -- 毫秒级时间戳，用于精确排序\n      -- 工具调用相关字段\n      tool_name TEXT, -- 工具名称\n      tool_args TEXT, -- 工具参数 (JSON)\n      tool_result TEXT, -- 工具结果 (JSON)\n      tool_status TEXT CHECK (tool_status IN ('executing', 'completed', 'error')), -- 工具状态\n      tool_execution_time INTEGER, -- 工具执行时间(毫秒)\n      tool_error TEXT, -- 工具错误信息\n      -- Ollama生成统计信息\n      total_duration INTEGER,\n      load_duration INTEGER,\n      prompt_eval_count INTEGER,\n      prompt_eval_duration INTEGER,\n      eval_count INTEGER,\n      eval_duration INTEGER,\n      FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE\n    );\n\n    -- MCP服务器统一配置表\n    CREATE TABLE IF NOT EXISTS mcp_servers (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      name TEXT NOT NULL UNIQUE,\n      display_name TEXT NOT NULL,\n      description TEXT,\n      type TEXT NOT NULL CHECK (type IN ('stdio', 'sse', 'streamable-http')),\n      status TEXT NOT NULL DEFAULT 'disconnected' CHECK (status IN ('connected', 'disconnected', 'error', 'connecting')),\n      enabled BOOLEAN NOT NULL DEFAULT 1,\n      \n      -- STDIO配置\n      command TEXT,\n      args TEXT, -- JSON数组格式\n      working_directory TEXT,\n      \n      -- SSE/HTTP配置\n      url TEXT,\n      base_url TEXT,\n      port INTEGER,\n      path TEXT DEFAULT '/',\n      protocol TEXT DEFAULT 'http' CHECK (protocol IN ('http', 'https')),\n      \n      -- 通用配置\n      headers TEXT, -- JSON对象格式\n      auth_type TEXT CHECK (auth_type IN ('none', 'bearer', 'basic', 'api_key')),\n      auth_config TEXT, -- JSON格式\n      timeout_ms INTEGER DEFAULT 30000,\n      retry_attempts INTEGER DEFAULT 3,\n      retry_delay_ms INTEGER DEFAULT 1000,\n      \n      -- 扩展配置\n      extra_config TEXT, -- JSON格式，存储其他特殊配置\n      \n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      last_connected_at DATETIME,\n      error_message TEXT\n    );\n\n    -- MCP工具表\n    CREATE TABLE IF NOT EXISTS mcp_tools (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      server_id INTEGER NOT NULL,\n      name TEXT NOT NULL,\n      description TEXT,\n      input_schema TEXT, -- JSON格式存储工具的输入参数模式\n      is_available BOOLEAN DEFAULT 1,\n      enabled BOOLEAN DEFAULT 1, -- 工具是否启用（在对话页面可见）\n      last_used_at DATETIME,\n      usage_count INTEGER DEFAULT 0,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (server_id) REFERENCES mcp_servers (id) ON DELETE CASCADE,\n      UNIQUE(server_id, name)\n    );\n\n    -- MCP工具调用记录表\n    CREATE TABLE IF NOT EXISTS mcp_tool_calls (\n      id INTEGER PRIMARY KEY AUTOINCREMENT,\n      tool_id INTEGER NOT NULL,\n      conversation_id INTEGER NOT NULL, -- 关联到对话\n      message_id INTEGER, -- 关联到具体的消息（可选）\n      input_args TEXT, -- JSON格式存储输入参数\n      output_result TEXT, -- JSON格式存储输出结果\n      execution_time_ms INTEGER,\n      status TEXT NOT NULL CHECK (status IN ('success', 'error', 'timeout')),\n      error_message TEXT,\n      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,\n      FOREIGN KEY (tool_id) REFERENCES mcp_tools (id) ON DELETE CASCADE,\n      FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE,\n      FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE SET NULL\n    );\n\n    -- 原有索引\n    CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);\n    CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON conversations(updated_at DESC);\n    \n    -- MCP相关索引\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_type ON mcp_servers(type);\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_status ON mcp_servers(status);\n    CREATE INDEX IF NOT EXISTS idx_mcp_servers_enabled ON mcp_servers(enabled);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_server_id ON mcp_tools(server_id);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_name ON mcp_tools(name);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tools_available ON mcp_tools(is_available);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_conversation_id ON mcp_tool_calls(conversation_id);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_tool_id ON mcp_tool_calls(tool_id);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_message_id ON mcp_tool_calls(message_id);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_created_at ON mcp_tool_calls(created_at DESC);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_status ON mcp_tool_calls(status);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_conv_time ON mcp_tool_calls(conversation_id, created_at DESC);\n    CREATE INDEX IF NOT EXISTS idx_mcp_tool_calls_conv_status ON mcp_tool_calls(conversation_id, status);\n  `;\n    // 执行初始化SQL\n    db.exec(initSQL);\n    // 数据库迁移：为messages表添加timestamp列（如果不存在）\n    try {\n        // 检查timestamp列是否存在\n        const columns = db.prepare(\"PRAGMA table_info(messages)\").all();\n        const hasTimestamp = columns.some((col)=>col.name === 'timestamp');\n        if (!hasTimestamp) {\n            console.log('正在为messages表添加timestamp列...');\n            db.exec('ALTER TABLE messages ADD COLUMN timestamp INTEGER');\n            // 为现有消息设置timestamp（基于created_at）\n            const updateTimestamp = db.prepare(`\n        UPDATE messages \n        SET timestamp = CAST((julianday(created_at) - 2440587.5) * 86400000 AS INTEGER)\n        WHERE timestamp IS NULL\n      `);\n            updateTimestamp.run();\n            console.log('messages表timestamp列添加完成');\n        }\n    } catch (error) {\n        console.error('数据库迁移失败:', error);\n    }\n    console.log('数据库初始化完成');\n    // 运行时间戳迁移（如果需要）\n    try {\n        const { autoMigrateIfNeeded } = __webpack_require__(/*! ./migrate-timestamps */ \"(rsc)/./src/lib/database/migrate-timestamps.ts\");\n        autoMigrateIfNeeded();\n    } catch (error) {\n        console.error('时间戳迁移失败:', error);\n    }\n};\nif (!global.__db_initialized) {\n    initializeDatabase();\n    global.__db_initialized = true;\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (db);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/connection.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/conversations.ts":
/*!*******************************************!*\
  !*** ./src/lib/database/conversations.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conversationOperations: () => (/* binding */ conversationOperations),\n/* harmony export */   conversationQueries: () => (/* binding */ conversationQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n// 对话相关查询语句\nconst conversationQueries = {\n    // 创建新对话\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO conversations (title, model)\n    VALUES (?, ?)\n  `),\n    // 获取所有对话\n    getAll: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM conversations\n    ORDER BY updated_at DESC\n  `),\n    // 根据ID获取对话\n    getById: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM conversations\n    WHERE id = ?\n  `),\n    // 更新对话标题\n    updateTitle: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE conversations\n    SET title = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 更新对话的最后更新时间\n    updateTimestamp: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE conversations\n    SET updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 删除对话\n    delete: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM conversations\n    WHERE id = ?\n  `)\n};\n// 对话数据库操作函数\nconst conversationOperations = {\n    // 创建新对话\n    create (data) {\n        const result = conversationQueries.create.run(data.title, data.model);\n        return result.lastInsertRowid;\n    },\n    // 获取所有对话\n    getAll () {\n        return conversationQueries.getAll.all();\n    },\n    // 根据ID获取对话\n    getById (id) {\n        return conversationQueries.getById.get(id);\n    },\n    // 更新对话标题\n    updateTitle (id, title) {\n        conversationQueries.updateTitle.run(title, id);\n    },\n    // 更新对话时间戳\n    updateTimestamp (id) {\n        conversationQueries.updateTimestamp.run(id);\n    },\n    // 删除对话\n    delete (id) {\n        conversationQueries.delete.run(id);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/conversations.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/index.ts":
/*!***********************************!*\
  !*** ./src/lib/database/index.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   conversationOperations: () => (/* reexport safe */ _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations),\n/* harmony export */   conversationQueries: () => (/* reexport safe */ _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationQueries),\n/* harmony export */   db: () => (/* reexport safe */ _connection__WEBPACK_IMPORTED_MODULE_0__.db),\n/* harmony export */   dbOperations: () => (/* binding */ dbOperations),\n/* harmony export */   \"default\": () => (/* reexport safe */ _connection__WEBPACK_IMPORTED_MODULE_0__.db),\n/* harmony export */   initializeDatabase: () => (/* reexport safe */ _connection__WEBPACK_IMPORTED_MODULE_0__.initializeDatabase),\n/* harmony export */   mcpDbOperations: () => (/* binding */ mcpDbOperations),\n/* harmony export */   mcpServerOperations: () => (/* reexport safe */ _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations),\n/* harmony export */   mcpServerQueries: () => (/* reexport safe */ _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerQueries),\n/* harmony export */   mcpToolCallOperations: () => (/* reexport safe */ _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations),\n/* harmony export */   mcpToolCallQueries: () => (/* reexport safe */ _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallQueries),\n/* harmony export */   mcpToolOperations: () => (/* reexport safe */ _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations),\n/* harmony export */   mcpToolQueries: () => (/* reexport safe */ _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolQueries),\n/* harmony export */   messageOperations: () => (/* reexport safe */ _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations),\n/* harmony export */   messageQueries: () => (/* reexport safe */ _messages__WEBPACK_IMPORTED_MODULE_3__.messageQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./types */ \"(rsc)/./src/lib/database/types.ts\");\n/* harmony import */ var _conversations__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conversations */ \"(rsc)/./src/lib/database/conversations.ts\");\n/* harmony import */ var _messages__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./messages */ \"(rsc)/./src/lib/database/messages.ts\");\n/* harmony import */ var _mcp_servers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./mcp-servers */ \"(rsc)/./src/lib/database/mcp-servers.ts\");\n/* harmony import */ var _mcp_tools__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./mcp-tools */ \"(rsc)/./src/lib/database/mcp-tools.ts\");\n/* harmony import */ var _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./mcp-tool-calls */ \"(rsc)/./src/lib/database/mcp-tool-calls.ts\");\n// 导出数据库连接\n\n// 导出所有类型定义\n\n// 导出各模块的操作函数\n\n\n\n\n\n// 为了保持向后兼容性，重新导出原有的操作对象\n\n\n\n\n\n// 兼容原有的 dbOperations 对象\nconst dbOperations = {\n    // 对话相关操作\n    createConversation: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.create,\n    getAllConversations: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.getAll,\n    getConversationById: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.getById,\n    updateConversationTitle: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.updateTitle,\n    updateConversationTimestamp: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.updateTimestamp,\n    deleteConversation: _conversations__WEBPACK_IMPORTED_MODULE_2__.conversationOperations.delete,\n    // 消息相关操作\n    createMessage: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.create,\n    getMessagesByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.getByConversationId,\n    deleteMessagesByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.deleteByConversationId,\n    getLastModelByConversationId: _messages__WEBPACK_IMPORTED_MODULE_3__.messageOperations.getLastModelByConversationId,\n    // MCP工具调用相关操作\n    getMcpToolCallsByConversationId: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.getByConversationId\n};\n// 兼容原有的 mcpDbOperations 对象\nconst mcpDbOperations = {\n    // MCP服务器相关操作\n    createMcpServer: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.create,\n    getAllMcpServers: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getAll,\n    getMcpServerById: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getById,\n    getMcpServerByName: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getByName,\n    getEnabledMcpServers: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.getEnabled,\n    updateMcpServerStatus: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.updateStatus,\n    deleteMcpServer: _mcp_servers__WEBPACK_IMPORTED_MODULE_4__.mcpServerOperations.delete,\n    // MCP工具相关操作\n    createMcpTool: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.create,\n    getMcpToolsByServerId: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getByServerId,\n    getMcpToolById: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getById,\n    getMcpToolByServerIdAndName: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getByServerIdAndName,\n    getAvailableMcpTools: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.getAvailable,\n    updateMcpToolUsage: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.updateUsage,\n    updateMcpToolAvailability: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.updateAvailability,\n    updateMcpToolEnabled: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.updateEnabled,\n    deleteMcpToolsByServerId: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.deleteByServerId,\n    deleteMcpTool: _mcp_tools__WEBPACK_IMPORTED_MODULE_5__.mcpToolOperations.delete,\n    // MCP工具调用相关操作\n    createMcpToolCall: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.create,\n    getMcpToolCallsByConversationId: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.getByConversationId,\n    getMcpToolCallsByToolId: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.getByToolId,\n    getRecentMcpToolCalls: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.getRecent,\n    getMcpToolCallStats: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.getStats,\n    deleteMcpToolCallsByConversationId: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.deleteByConversationId,\n    deleteMcpToolCallsByToolId: _mcp_tool_calls__WEBPACK_IMPORTED_MODULE_6__.mcpToolCallOperations.deleteByToolId\n};\n// 默认导出数据库连接（保持兼容性）\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/mcp-servers.ts":
/*!*****************************************!*\
  !*** ./src/lib/database/mcp-servers.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mcpServerOperations: () => (/* binding */ mcpServerOperations),\n/* harmony export */   mcpServerQueries: () => (/* binding */ mcpServerQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n// MCP服务器相关查询语句\nconst mcpServerQueries = {\n    // 创建MCP服务器\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO mcp_servers (\n      name, display_name, description, type, enabled,\n      command, args, working_directory,\n      url, base_url, port, path, protocol,\n      headers, auth_type, auth_config, timeout_ms, retry_attempts, retry_delay_ms,\n      extra_config\n    )\n    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n  `),\n    // 获取所有MCP服务器\n    getAll: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    ORDER BY created_at DESC\n  `),\n    // 根据ID获取MCP服务器\n    getById: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    WHERE id = ?\n  `),\n    // 根据名称获取MCP服务器\n    getByName: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    WHERE name = ?\n  `),\n    // 获取启用的MCP服务器\n    getEnabled: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_servers\n    WHERE enabled = 1\n    ORDER BY created_at DESC\n  `),\n    // 更新MCP服务器状态\n    updateStatus: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_servers\n    SET status = ?, error_message = ?, updated_at = CURRENT_TIMESTAMP,\n        last_connected_at = CASE WHEN ? = 'connected' THEN CURRENT_TIMESTAMP ELSE last_connected_at END\n    WHERE id = ?\n  `),\n    // 更新MCP服务器配置\n    update: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_servers\n    SET display_name = ?, description = ?, type = ?, enabled = ?,\n        command = ?, args = ?, working_directory = ?,\n        url = ?, base_url = ?, port = ?, path = ?, protocol = ?,\n        headers = ?, auth_type = ?, auth_config = ?, timeout_ms = ?, retry_attempts = ?, retry_delay_ms = ?,\n        extra_config = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 删除MCP服务器\n    delete: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_servers\n    WHERE id = ?\n  `)\n};\n// MCP服务器数据库操作函数\nconst mcpServerOperations = {\n    // 创建MCP服务器\n    create (data) {\n        const result = mcpServerQueries.create.run(data.name, data.display_name, data.description || null, data.type, Boolean(data.enabled ?? true) ? 1 : 0, data.command || null, data.args ? JSON.stringify(data.args) : null, data.working_directory || null, data.url || null, data.base_url || null, data.port ? Number(data.port) : null, data.path || null, data.protocol || null, data.headers ? JSON.stringify(data.headers) : null, data.auth_type || null, data.auth_config ? JSON.stringify(data.auth_config) : null, data.timeout_ms ? Number(data.timeout_ms) : null, data.retry_attempts ? Number(data.retry_attempts) : null, data.retry_delay_ms ? Number(data.retry_delay_ms) : null, data.extra_config ? JSON.stringify(data.extra_config) : null);\n        return result.lastInsertRowid;\n    },\n    // 获取所有MCP服务器\n    getAll () {\n        return mcpServerQueries.getAll.all();\n    },\n    // 根据ID获取MCP服务器\n    getById (id) {\n        return mcpServerQueries.getById.get(id);\n    },\n    // 根据名称获取MCP服务器\n    getByName (name) {\n        return mcpServerQueries.getByName.get(name);\n    },\n    // 获取启用的MCP服务器\n    getEnabled () {\n        return mcpServerQueries.getEnabled.all();\n    },\n    // 更新MCP服务器状态\n    updateStatus (id, status, errorMessage) {\n        mcpServerQueries.updateStatus.run(status, errorMessage || null, status, id);\n    },\n    // 删除MCP服务器\n    delete (id) {\n        mcpServerQueries.delete.run(id);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/mcp-servers.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/mcp-tool-calls.ts":
/*!********************************************!*\
  !*** ./src/lib/database/mcp-tool-calls.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mcpToolCallOperations: () => (/* binding */ mcpToolCallOperations),\n/* harmony export */   mcpToolCallQueries: () => (/* binding */ mcpToolCallQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var _mcp_tools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./mcp-tools */ \"(rsc)/./src/lib/database/mcp-tools.ts\");\n\n\n// MCP工具调用相关查询语句\nconst mcpToolCallQueries = {\n    // 创建工具调用记录\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO mcp_tool_calls (\n      tool_id, conversation_id, message_id, input_args, output_result,\n      execution_time_ms, status, error_message\n    )\n    VALUES (?, ?, ?, ?, ?, ?, ?, ?)\n  `),\n    // 获取对话的工具调用记录\n    getByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT tc.*, t.name as tool_name, s.name as server_name\n    FROM mcp_tool_calls tc\n    JOIN mcp_tools t ON tc.tool_id = t.id\n    JOIN mcp_servers s ON t.server_id = s.id\n    WHERE tc.conversation_id = ?\n    ORDER BY tc.created_at ASC\n  `),\n    // 获取工具的调用记录\n    getByToolId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tool_calls\n    WHERE tool_id = ?\n    ORDER BY created_at DESC\n  `),\n    // 获取最近的工具调用记录\n    getRecent: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT tc.*, t.name as tool_name, s.name as server_name\n    FROM mcp_tool_calls tc\n    JOIN mcp_tools t ON tc.tool_id = t.id\n    JOIN mcp_servers s ON t.server_id = s.id\n    ORDER BY tc.created_at DESC\n    LIMIT ?\n  `),\n    // 获取工具调用统计信息\n    getStats: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT \n      COUNT(*) as total_calls,\n      COUNT(CASE WHEN status = 'success' THEN 1 END) as success_calls,\n      COUNT(CASE WHEN status = 'error' THEN 1 END) as error_calls,\n      COUNT(CASE WHEN status = 'timeout' THEN 1 END) as timeout_calls,\n      AVG(execution_time_ms) as avg_execution_time\n    FROM mcp_tool_calls\n    WHERE tool_id = ?\n  `),\n    // 删除对话的工具调用记录\n    deleteByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_tool_calls\n    WHERE conversation_id = ?\n  `),\n    // 删除工具的调用记录\n    deleteByToolId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_tool_calls\n    WHERE tool_id = ?\n  `)\n};\n// MCP工具调用数据库操作函数\nconst mcpToolCallOperations = {\n    // 创建工具调用记录\n    create (data) {\n        const result = mcpToolCallQueries.create.run(data.tool_id, data.conversation_id, data.message_id || null, data.input_args ? JSON.stringify(data.input_args) : null, data.output_result ? JSON.stringify(data.output_result) : null, data.execution_time_ms || null, data.status, data.error_message || null);\n        // 更新工具使用统计\n        if (data.status === 'success') {\n            _mcp_tools__WEBPACK_IMPORTED_MODULE_1__.mcpToolOperations.updateUsage(data.tool_id);\n        }\n        return result.lastInsertRowid;\n    },\n    // 获取对话的工具调用记录\n    getByConversationId (conversationId) {\n        return mcpToolCallQueries.getByConversationId.all(conversationId);\n    },\n    // 获取工具的调用记录\n    getByToolId (toolId) {\n        return mcpToolCallQueries.getByToolId.all(toolId);\n    },\n    // 获取最近的工具调用记录\n    getRecent (limit = 50) {\n        return mcpToolCallQueries.getRecent.all(limit);\n    },\n    // 获取工具调用统计信息\n    getStats (toolId) {\n        return mcpToolCallQueries.getStats.get(toolId);\n    },\n    // 删除对话的工具调用记录\n    deleteByConversationId (conversationId) {\n        mcpToolCallQueries.deleteByConversationId.run(conversationId);\n    },\n    // 删除工具的调用记录\n    deleteByToolId (toolId) {\n        mcpToolCallQueries.deleteByToolId.run(toolId);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/mcp-tool-calls.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/mcp-tools.ts":
/*!***************************************!*\
  !*** ./src/lib/database/mcp-tools.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   mcpToolOperations: () => (/* binding */ mcpToolOperations),\n/* harmony export */   mcpToolQueries: () => (/* binding */ mcpToolQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n// MCP工具相关查询语句\nconst mcpToolQueries = {\n    // 创建MCP工具\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO mcp_tools (server_id, name, description, input_schema, is_available, enabled)\n    VALUES (?, ?, ?, ?, ?, ?)\n  `),\n    // 获取服务器的所有工具\n    getByServerId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tools\n    WHERE server_id = ?\n    ORDER BY name ASC\n  `),\n    // 根据ID获取工具\n    getById: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tools\n    WHERE id = ?\n  `),\n    // 根据服务器ID和工具名称获取工具\n    getByServerIdAndName: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM mcp_tools\n    WHERE server_id = ? AND name = ?\n  `),\n    // 获取可用的工具\n    getAvailable: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT t.*, s.name as server_name, s.status as server_status\n    FROM mcp_tools t\n    JOIN mcp_servers s ON t.server_id = s.id\n    WHERE t.is_available = 1 AND t.enabled = 1 AND s.enabled = 1 AND s.status = 'connected'\n    ORDER BY t.name ASC\n  `),\n    // 更新工具使用统计\n    updateUsage: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_tools\n    SET usage_count = usage_count + 1, last_used_at = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 更新工具可用性\n    updateAvailability: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_tools\n    SET is_available = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 更新工具启用状态\n    updateEnabled: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    UPDATE mcp_tools\n    SET enabled = ?, updated_at = CURRENT_TIMESTAMP\n    WHERE id = ?\n  `),\n    // 删除服务器的所有工具\n    deleteByServerId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_tools\n    WHERE server_id = ?\n  `),\n    // 删除工具\n    delete: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM mcp_tools\n    WHERE id = ?\n  `)\n};\n// MCP工具数据库操作函数\nconst mcpToolOperations = {\n    // 创建MCP工具\n    create (data) {\n        const result = mcpToolQueries.create.run(data.server_id, data.name, data.description || null, data.input_schema ? JSON.stringify(data.input_schema) : null, Boolean(data.is_available ?? true) ? 1 : 0, Boolean(data.enabled ?? true) ? 1 : 0 // 确保布尔值转换为数字\n        );\n        return result.lastInsertRowid;\n    },\n    // 获取服务器的所有工具\n    getByServerId (serverId) {\n        return mcpToolQueries.getByServerId.all(serverId);\n    },\n    // 根据ID获取工具\n    getById (id) {\n        return mcpToolQueries.getById.get(id);\n    },\n    // 根据服务器ID和工具名称获取工具\n    getByServerIdAndName (serverId, name) {\n        return mcpToolQueries.getByServerIdAndName.get(serverId, name);\n    },\n    // 获取可用的工具\n    getAvailable () {\n        return mcpToolQueries.getAvailable.all();\n    },\n    // 更新工具使用统计\n    updateUsage (toolId) {\n        mcpToolQueries.updateUsage.run(toolId);\n    },\n    // 更新工具可用性\n    updateAvailability (id, isAvailable) {\n        mcpToolQueries.updateAvailability.run(isAvailable ? 1 : 0, id);\n    },\n    // 更新工具启用状态\n    updateEnabled (id, enabled) {\n        mcpToolQueries.updateEnabled.run(enabled ? 1 : 0, id);\n    },\n    // 删除服务器的所有工具\n    deleteByServerId (serverId) {\n        mcpToolQueries.deleteByServerId.run(serverId);\n    },\n    // 删除工具\n    delete (id) {\n        mcpToolQueries.delete.run(id);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/mcp-tools.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/messages.ts":
/*!**************************************!*\
  !*** ./src/lib/database/messages.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   messageOperations: () => (/* binding */ messageOperations),\n/* harmony export */   messageQueries: () => (/* binding */ messageQueries)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n/* harmony import */ var _conversations__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./conversations */ \"(rsc)/./src/lib/database/conversations.ts\");\n\n\n// 消息相关查询语句\nconst messageQueries = {\n    // 创建新消息\n    create: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    INSERT INTO messages (\n      conversation_id, role, content, model, sequence_number, timestamp,\n      total_duration, load_duration, prompt_eval_count, prompt_eval_duration,\n      eval_count, eval_duration\n    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n  `),\n    // 获取对话的所有消息\n    getByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT * FROM messages\n    WHERE conversation_id = ?\n    ORDER BY id ASC\n  `),\n    // 删除对话的所有消息\n    deleteByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    DELETE FROM messages\n    WHERE conversation_id = ?\n  `),\n    // 获取对话中最后使用的模型\n    getLastModelByConversationId: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT model FROM messages\n    WHERE conversation_id = ? AND model IS NOT NULL\n    ORDER BY created_at DESC\n    LIMIT 1\n  `),\n    // 获取对话中下一个可用的序列号\n    getNextSequenceNumber: _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n    SELECT COALESCE(MAX(sequence_number), 0) + 1 as next_sequence\n    FROM messages\n    WHERE conversation_id = ?\n  `)\n};\n// 消息数据库操作函数\nconst messageOperations = {\n    // 创建新消息\n    create (data) {\n        // 简化：不再使用sequence_number，只依赖自增ID\n        // 生成时间戳（毫秒级）\n        const timestamp = Date.now();\n        const result = messageQueries.create.run(data.conversation_id, data.role, data.content, data.model || null, 0, timestamp, data.total_duration || null, data.load_duration || null, data.prompt_eval_count || null, data.prompt_eval_duration || null, data.eval_count || null, data.eval_duration || null);\n        // 更新对话的时间戳\n        _conversations__WEBPACK_IMPORTED_MODULE_1__.conversationOperations.updateTimestamp(data.conversation_id);\n        return result.lastInsertRowid;\n    },\n    // 获取对话的所有消息\n    getByConversationId (conversationId) {\n        return messageQueries.getByConversationId.all(conversationId);\n    },\n    // 删除对话的所有消息\n    deleteByConversationId (conversationId) {\n        messageQueries.deleteByConversationId.run(conversationId);\n    },\n    // 获取对话中最后使用的模型\n    getLastModelByConversationId (conversationId) {\n        const result = messageQueries.getLastModelByConversationId.get(conversationId);\n        return result?.model || null;\n    },\n    // 获取对话中下一个可用的序列号（已废弃，保留兼容性）\n    getNextSequenceNumber (conversationId) {\n        const result = messageQueries.getNextSequenceNumber.get(conversationId);\n        return result?.next_sequence || 1;\n    },\n    // 创建工具调用消息\n    createToolCall (data) {\n        const timestamp = Date.now();\n        const result = messageQueries.create.run(data.conversation_id, 'tool_call', `工具调用: ${data.tool_name}`, null, 0, timestamp, null, null, null, null, null, null // 统计信息字段\n        );\n        // 更新工具相关字段\n        if (result.lastInsertRowid) {\n            const updateToolFields = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n        UPDATE messages SET\n          tool_name = ?,\n          tool_args = ?,\n          tool_result = ?,\n          tool_status = ?,\n          tool_execution_time = ?,\n          tool_error = ?\n        WHERE id = ?\n      `);\n            updateToolFields.run(data.tool_name, JSON.stringify(data.tool_args), data.tool_result ? JSON.stringify(data.tool_result) : null, data.tool_status, data.tool_execution_time || null, data.tool_error || null, result.lastInsertRowid);\n        }\n        _conversations__WEBPACK_IMPORTED_MODULE_1__.conversationOperations.updateTimestamp(data.conversation_id);\n        return result.lastInsertRowid;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/messages.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/migrate-timestamps.ts":
/*!************************************************!*\
  !*** ./src/lib/database/migrate-timestamps.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autoMigrateIfNeeded: () => (/* binding */ autoMigrateIfNeeded),\n/* harmony export */   migrateMessageTimestamps: () => (/* binding */ migrateMessageTimestamps),\n/* harmony export */   needsTimestampMigration: () => (/* binding */ needsTimestampMigration)\n/* harmony export */ });\n/* harmony import */ var _connection__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./connection */ \"(rsc)/./src/lib/database/connection.ts\");\n\n/**\n * 数据库迁移脚本：为现有消息添加timestamp字段\n * 这个脚本会为所有没有timestamp的消息生成基于created_at的时间戳\n */ function migrateMessageTimestamps() {\n    console.log('开始迁移消息时间戳...');\n    try {\n        // 开始事务\n        _connection__WEBPACK_IMPORTED_MODULE_0__.db.exec('BEGIN TRANSACTION');\n        // 查询所有没有timestamp或timestamp为null的消息\n        const messagesWithoutTimestamp = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n      SELECT id, created_at, sequence_number, conversation_id\n      FROM messages \n      WHERE timestamp IS NULL \n      ORDER BY conversation_id, created_at ASC, sequence_number ASC\n    `).all();\n        console.log(`找到 ${messagesWithoutTimestamp.length} 条需要迁移的消息`);\n        if (messagesWithoutTimestamp.length === 0) {\n            console.log('没有需要迁移的消息');\n            _connection__WEBPACK_IMPORTED_MODULE_0__.db.exec('COMMIT');\n            return;\n        }\n        // 准备更新语句\n        const updateTimestamp = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n      UPDATE messages \n      SET timestamp = ? \n      WHERE id = ?\n    `);\n        // 按对话分组处理，确保同一对话内的消息时间戳递增\n        const conversationGroups = new Map();\n        messagesWithoutTimestamp.forEach((message)=>{\n            if (!conversationGroups.has(message.conversation_id)) {\n                conversationGroups.set(message.conversation_id, []);\n            }\n            conversationGroups.get(message.conversation_id).push(message);\n        });\n        let updatedCount = 0;\n        // 为每个对话的消息生成递增的时间戳\n        conversationGroups.forEach((messages, conversationId)=>{\n            console.log(`处理对话 ${conversationId} 的 ${messages.length} 条消息`);\n            messages.forEach((message, index)=>{\n                // 基于created_at生成时间戳，并加上索引确保递增\n                const baseTimestamp = new Date(message.created_at).getTime();\n                const timestamp = baseTimestamp + index * 1000; // 每条消息间隔1秒\n                updateTimestamp.run(timestamp, message.id);\n                updatedCount++;\n            });\n        });\n        // 提交事务\n        _connection__WEBPACK_IMPORTED_MODULE_0__.db.exec('COMMIT');\n        console.log(`成功迁移 ${updatedCount} 条消息的时间戳`);\n        // 验证迁移结果\n        const remainingCount = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n      SELECT COUNT(*) as count \n      FROM messages \n      WHERE timestamp IS NULL\n    `).get();\n        if (remainingCount.count === 0) {\n            console.log('✅ 时间戳迁移完成，所有消息都有有效的timestamp');\n        } else {\n            console.warn(`⚠️ 仍有 ${remainingCount.count} 条消息没有timestamp`);\n        }\n    } catch (error) {\n        // 回滚事务\n        _connection__WEBPACK_IMPORTED_MODULE_0__.db.exec('ROLLBACK');\n        console.error('迁移失败，已回滚:', error);\n        throw error;\n    }\n}\n/**\n * 检查是否需要运行迁移\n */ function needsTimestampMigration() {\n    try {\n        const result = _connection__WEBPACK_IMPORTED_MODULE_0__.db.prepare(`\n      SELECT COUNT(*) as count \n      FROM messages \n      WHERE timestamp IS NULL\n    `).get();\n        return result.count > 0;\n    } catch (error) {\n        console.error('检查迁移需求失败:', error);\n        return false;\n    }\n}\n/**\n * 自动运行迁移（如果需要）\n */ function autoMigrateIfNeeded() {\n    if (needsTimestampMigration()) {\n        console.log('检测到需要迁移时间戳，开始自动迁移...');\n        migrateMessageTimestamps();\n    } else {\n        console.log('所有消息都有有效的timestamp，无需迁移');\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/migrate-timestamps.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database/types.ts":
/*!***********************************!*\
  !*** ./src/lib/database/types.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n// 对话相关接口\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database/types.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "better-sqlite3":
/*!*********************************!*\
  !*** external "better-sqlite3" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("better-sqlite3");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fconversations%2Froute&page=%2Fapi%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fconversations%2Froute.ts&appDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CZack%5CDesktop%5CRP30_kunagent%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();