import { db } from './connection';

/**
 * 数据库迁移脚本：为现有消息添加timestamp字段
 * 这个脚本会为所有没有timestamp的消息生成基于created_at的时间戳
 */
export function migrateMessageTimestamps(): void {
  console.log('开始迁移消息时间戳...');
  
  try {
    // 开始事务
    db.exec('BEGIN TRANSACTION');
    
    // 查询所有没有timestamp或timestamp为null的消息
    const messagesWithoutTimestamp = db.prepare(`
      SELECT id, created_at, sequence_number, conversation_id
      FROM messages 
      WHERE timestamp IS NULL 
      ORDER BY conversation_id, created_at ASC, sequence_number ASC
    `).all() as Array<{
      id: number;
      created_at: string;
      sequence_number: number;
      conversation_id: number;
    }>;
    
    console.log(`找到 ${messagesWithoutTimestamp.length} 条需要迁移的消息`);
    
    if (messagesWithoutTimestamp.length === 0) {
      console.log('没有需要迁移的消息');
      db.exec('COMMIT');
      return;
    }
    
    // 准备更新语句
    const updateTimestamp = db.prepare(`
      UPDATE messages 
      SET timestamp = ? 
      WHERE id = ?
    `);
    
    // 按对话分组处理，确保同一对话内的消息时间戳递增
    const conversationGroups = new Map<number, Array<typeof messagesWithoutTimestamp[0]>>();
    
    messagesWithoutTimestamp.forEach(message => {
      if (!conversationGroups.has(message.conversation_id)) {
        conversationGroups.set(message.conversation_id, []);
      }
      conversationGroups.get(message.conversation_id)!.push(message);
    });
    
    let updatedCount = 0;
    
    // 为每个对话的消息生成递增的时间戳
    conversationGroups.forEach((messages, conversationId) => {
      console.log(`处理对话 ${conversationId} 的 ${messages.length} 条消息`);
      
      messages.forEach((message, index) => {
        // 基于created_at生成时间戳，并加上索引确保递增
        const baseTimestamp = new Date(message.created_at).getTime();
        const timestamp = baseTimestamp + index * 1000; // 每条消息间隔1秒
        
        updateTimestamp.run(timestamp, message.id);
        updatedCount++;
      });
    });
    
    // 提交事务
    db.exec('COMMIT');
    
    console.log(`成功迁移 ${updatedCount} 条消息的时间戳`);
    
    // 验证迁移结果
    const remainingCount = db.prepare(`
      SELECT COUNT(*) as count 
      FROM messages 
      WHERE timestamp IS NULL
    `).get() as { count: number };
    
    if (remainingCount.count === 0) {
      console.log('✅ 时间戳迁移完成，所有消息都有有效的timestamp');
    } else {
      console.warn(`⚠️ 仍有 ${remainingCount.count} 条消息没有timestamp`);
    }
    
  } catch (error) {
    // 回滚事务
    db.exec('ROLLBACK');
    console.error('迁移失败，已回滚:', error);
    throw error;
  }
}

/**
 * 检查是否需要运行迁移
 */
export function needsTimestampMigration(): boolean {
  try {
    const result = db.prepare(`
      SELECT COUNT(*) as count 
      FROM messages 
      WHERE timestamp IS NULL
    `).get() as { count: number };
    
    return result.count > 0;
  } catch (error) {
    console.error('检查迁移需求失败:', error);
    return false;
  }
}

/**
 * 自动运行迁移（如果需要）
 */
export function autoMigrateIfNeeded(): void {
  if (needsTimestampMigration()) {
    console.log('检测到需要迁移时间戳，开始自动迁移...');
    migrateMessageTimestamps();
  } else {
    console.log('所有消息都有有效的timestamp，无需迁移');
  }
}
