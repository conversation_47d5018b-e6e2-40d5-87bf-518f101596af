"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/eventsource";
exports.ids = ["vendor-chunks/eventsource"];
exports.modules = {

/***/ "(rsc)/./node_modules/eventsource/dist/index.js":
/*!************************************************!*\
  !*** ./node_modules/eventsource/dist/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorEvent: () => (/* binding */ ErrorEvent),\n/* harmony export */   EventSource: () => (/* binding */ EventSource)\n/* harmony export */ });\n/* harmony import */ var eventsource_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! eventsource-parser */ \"(rsc)/./node_modules/eventsource-parser/dist/index.js\");\n\nclass ErrorEvent extends Event {\n  /**\n   * Constructs a new `ErrorEvent` instance. This is typically not called directly,\n   * but rather emitted by the `EventSource` object when an error occurs.\n   *\n   * @param type - The type of the event (should be \"error\")\n   * @param errorEventInitDict - Optional properties to include in the error event\n   */\n  constructor(type, errorEventInitDict) {\n    var _a, _b;\n    super(type), this.code = (_a = errorEventInitDict == null ? void 0 : errorEventInitDict.code) != null ? _a : void 0, this.message = (_b = errorEventInitDict == null ? void 0 : errorEventInitDict.message) != null ? _b : void 0;\n  }\n  /**\n   * Node.js \"hides\" the `message` and `code` properties of the `ErrorEvent` instance,\n   * when it is `console.log`'ed. This makes it harder to debug errors. To ease debugging,\n   * we explicitly include the properties in the `inspect` method.\n   *\n   * This is automatically called by Node.js when you `console.log` an instance of this class.\n   *\n   * @param _depth - The current depth\n   * @param options - The options passed to `util.inspect`\n   * @param inspect - The inspect function to use (prevents having to import it from `util`)\n   * @returns A string representation of the error\n   */\n  [Symbol.for(\"nodejs.util.inspect.custom\")](_depth, options, inspect) {\n    return inspect(inspectableError(this), options);\n  }\n  /**\n   * Deno \"hides\" the `message` and `code` properties of the `ErrorEvent` instance,\n   * when it is `console.log`'ed. This makes it harder to debug errors. To ease debugging,\n   * we explicitly include the properties in the `inspect` method.\n   *\n   * This is automatically called by Deno when you `console.log` an instance of this class.\n   *\n   * @param inspect - The inspect function to use (prevents having to import it from `util`)\n   * @param options - The options passed to `Deno.inspect`\n   * @returns A string representation of the error\n   */\n  [Symbol.for(\"Deno.customInspect\")](inspect, options) {\n    return inspect(inspectableError(this), options);\n  }\n}\nfunction syntaxError(message) {\n  const DomException = globalThis.DOMException;\n  return typeof DomException == \"function\" ? new DomException(message, \"SyntaxError\") : new SyntaxError(message);\n}\nfunction flattenError(err) {\n  return err instanceof Error ? \"errors\" in err && Array.isArray(err.errors) ? err.errors.map(flattenError).join(\", \") : \"cause\" in err && err.cause instanceof Error ? `${err}: ${flattenError(err.cause)}` : err.message : `${err}`;\n}\nfunction inspectableError(err) {\n  return {\n    type: err.type,\n    message: err.message,\n    code: err.code,\n    defaultPrevented: err.defaultPrevented,\n    cancelable: err.cancelable,\n    timeStamp: err.timeStamp\n  };\n}\nvar __typeError = (msg) => {\n  throw TypeError(msg);\n}, __accessCheck = (obj, member, msg) => member.has(obj) || __typeError(\"Cannot \" + msg), __privateGet = (obj, member, getter) => (__accessCheck(obj, member, \"read from private field\"), getter ? getter.call(obj) : member.get(obj)), __privateAdd = (obj, member, value) => member.has(obj) ? __typeError(\"Cannot add the same private member more than once\") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value), __privateSet = (obj, member, value, setter) => (__accessCheck(obj, member, \"write to private field\"), member.set(obj, value), value), __privateMethod = (obj, member, method) => (__accessCheck(obj, member, \"access private method\"), method), _readyState, _url, _redirectUrl, _withCredentials, _fetch, _reconnectInterval, _reconnectTimer, _lastEventId, _controller, _parser, _onError, _onMessage, _onOpen, _EventSource_instances, connect_fn, _onFetchResponse, _onFetchError, getRequestOptions_fn, _onEvent, _onRetryChange, failConnection_fn, scheduleReconnect_fn, _reconnect;\nclass EventSource extends EventTarget {\n  constructor(url, eventSourceInitDict) {\n    var _a, _b;\n    super(), __privateAdd(this, _EventSource_instances), this.CONNECTING = 0, this.OPEN = 1, this.CLOSED = 2, __privateAdd(this, _readyState), __privateAdd(this, _url), __privateAdd(this, _redirectUrl), __privateAdd(this, _withCredentials), __privateAdd(this, _fetch), __privateAdd(this, _reconnectInterval), __privateAdd(this, _reconnectTimer), __privateAdd(this, _lastEventId, null), __privateAdd(this, _controller), __privateAdd(this, _parser), __privateAdd(this, _onError, null), __privateAdd(this, _onMessage, null), __privateAdd(this, _onOpen, null), __privateAdd(this, _onFetchResponse, async (response) => {\n      var _a2;\n      __privateGet(this, _parser).reset();\n      const { body, redirected, status, headers } = response;\n      if (status === 204) {\n        __privateMethod(this, _EventSource_instances, failConnection_fn).call(this, \"Server sent HTTP 204, not reconnecting\", 204), this.close();\n        return;\n      }\n      if (redirected ? __privateSet(this, _redirectUrl, new URL(response.url)) : __privateSet(this, _redirectUrl, void 0), status !== 200) {\n        __privateMethod(this, _EventSource_instances, failConnection_fn).call(this, `Non-200 status code (${status})`, status);\n        return;\n      }\n      if (!(headers.get(\"content-type\") || \"\").startsWith(\"text/event-stream\")) {\n        __privateMethod(this, _EventSource_instances, failConnection_fn).call(this, 'Invalid content type, expected \"text/event-stream\"', status);\n        return;\n      }\n      if (__privateGet(this, _readyState) === this.CLOSED)\n        return;\n      __privateSet(this, _readyState, this.OPEN);\n      const openEvent = new Event(\"open\");\n      if ((_a2 = __privateGet(this, _onOpen)) == null || _a2.call(this, openEvent), this.dispatchEvent(openEvent), typeof body != \"object\" || !body || !(\"getReader\" in body)) {\n        __privateMethod(this, _EventSource_instances, failConnection_fn).call(this, \"Invalid response body, expected a web ReadableStream\", status), this.close();\n        return;\n      }\n      const decoder = new TextDecoder(), reader = body.getReader();\n      let open = !0;\n      do {\n        const { done, value } = await reader.read();\n        value && __privateGet(this, _parser).feed(decoder.decode(value, { stream: !done })), done && (open = !1, __privateGet(this, _parser).reset(), __privateMethod(this, _EventSource_instances, scheduleReconnect_fn).call(this));\n      } while (open);\n    }), __privateAdd(this, _onFetchError, (err) => {\n      __privateSet(this, _controller, void 0), !(err.name === \"AbortError\" || err.type === \"aborted\") && __privateMethod(this, _EventSource_instances, scheduleReconnect_fn).call(this, flattenError(err));\n    }), __privateAdd(this, _onEvent, (event) => {\n      typeof event.id == \"string\" && __privateSet(this, _lastEventId, event.id);\n      const messageEvent = new MessageEvent(event.event || \"message\", {\n        data: event.data,\n        origin: __privateGet(this, _redirectUrl) ? __privateGet(this, _redirectUrl).origin : __privateGet(this, _url).origin,\n        lastEventId: event.id || \"\"\n      });\n      __privateGet(this, _onMessage) && (!event.event || event.event === \"message\") && __privateGet(this, _onMessage).call(this, messageEvent), this.dispatchEvent(messageEvent);\n    }), __privateAdd(this, _onRetryChange, (value) => {\n      __privateSet(this, _reconnectInterval, value);\n    }), __privateAdd(this, _reconnect, () => {\n      __privateSet(this, _reconnectTimer, void 0), __privateGet(this, _readyState) === this.CONNECTING && __privateMethod(this, _EventSource_instances, connect_fn).call(this);\n    });\n    try {\n      if (url instanceof URL)\n        __privateSet(this, _url, url);\n      else if (typeof url == \"string\")\n        __privateSet(this, _url, new URL(url, getBaseURL()));\n      else\n        throw new Error(\"Invalid URL\");\n    } catch {\n      throw syntaxError(\"An invalid or illegal string was specified\");\n    }\n    __privateSet(this, _parser, (0,eventsource_parser__WEBPACK_IMPORTED_MODULE_0__.createParser)({\n      onEvent: __privateGet(this, _onEvent),\n      onRetry: __privateGet(this, _onRetryChange)\n    })), __privateSet(this, _readyState, this.CONNECTING), __privateSet(this, _reconnectInterval, 3e3), __privateSet(this, _fetch, (_a = eventSourceInitDict == null ? void 0 : eventSourceInitDict.fetch) != null ? _a : globalThis.fetch), __privateSet(this, _withCredentials, (_b = eventSourceInitDict == null ? void 0 : eventSourceInitDict.withCredentials) != null ? _b : !1), __privateMethod(this, _EventSource_instances, connect_fn).call(this);\n  }\n  /**\n   * Returns the state of this EventSource object's connection. It can have the values described below.\n   *\n   * [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/readyState)\n   *\n   * Note: typed as `number` instead of `0 | 1 | 2` for compatibility with the `EventSource` interface,\n   * defined in the TypeScript `dom` library.\n   *\n   * @public\n   */\n  get readyState() {\n    return __privateGet(this, _readyState);\n  }\n  /**\n   * Returns the URL providing the event stream.\n   *\n   * [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/url)\n   *\n   * @public\n   */\n  get url() {\n    return __privateGet(this, _url).href;\n  }\n  /**\n   * Returns true if the credentials mode for connection requests to the URL providing the event stream is set to \"include\", and false otherwise.\n   *\n   * [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/withCredentials)\n   */\n  get withCredentials() {\n    return __privateGet(this, _withCredentials);\n  }\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/error_event) */\n  get onerror() {\n    return __privateGet(this, _onError);\n  }\n  set onerror(value) {\n    __privateSet(this, _onError, value);\n  }\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/message_event) */\n  get onmessage() {\n    return __privateGet(this, _onMessage);\n  }\n  set onmessage(value) {\n    __privateSet(this, _onMessage, value);\n  }\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/open_event) */\n  get onopen() {\n    return __privateGet(this, _onOpen);\n  }\n  set onopen(value) {\n    __privateSet(this, _onOpen, value);\n  }\n  addEventListener(type, listener, options) {\n    const listen = listener;\n    super.addEventListener(type, listen, options);\n  }\n  removeEventListener(type, listener, options) {\n    const listen = listener;\n    super.removeEventListener(type, listen, options);\n  }\n  /**\n   * Aborts any instances of the fetch algorithm started for this EventSource object, and sets the readyState attribute to CLOSED.\n   *\n   * [MDN Reference](https://developer.mozilla.org/docs/Web/API/EventSource/close)\n   *\n   * @public\n   */\n  close() {\n    __privateGet(this, _reconnectTimer) && clearTimeout(__privateGet(this, _reconnectTimer)), __privateGet(this, _readyState) !== this.CLOSED && (__privateGet(this, _controller) && __privateGet(this, _controller).abort(), __privateSet(this, _readyState, this.CLOSED), __privateSet(this, _controller, void 0));\n  }\n}\n_readyState = /* @__PURE__ */ new WeakMap(), _url = /* @__PURE__ */ new WeakMap(), _redirectUrl = /* @__PURE__ */ new WeakMap(), _withCredentials = /* @__PURE__ */ new WeakMap(), _fetch = /* @__PURE__ */ new WeakMap(), _reconnectInterval = /* @__PURE__ */ new WeakMap(), _reconnectTimer = /* @__PURE__ */ new WeakMap(), _lastEventId = /* @__PURE__ */ new WeakMap(), _controller = /* @__PURE__ */ new WeakMap(), _parser = /* @__PURE__ */ new WeakMap(), _onError = /* @__PURE__ */ new WeakMap(), _onMessage = /* @__PURE__ */ new WeakMap(), _onOpen = /* @__PURE__ */ new WeakMap(), _EventSource_instances = /* @__PURE__ */ new WeakSet(), /**\n* Connect to the given URL and start receiving events\n*\n* @internal\n*/\nconnect_fn = function() {\n  __privateSet(this, _readyState, this.CONNECTING), __privateSet(this, _controller, new AbortController()), __privateGet(this, _fetch)(__privateGet(this, _url), __privateMethod(this, _EventSource_instances, getRequestOptions_fn).call(this)).then(__privateGet(this, _onFetchResponse)).catch(__privateGet(this, _onFetchError));\n}, _onFetchResponse = /* @__PURE__ */ new WeakMap(), _onFetchError = /* @__PURE__ */ new WeakMap(), /**\n* Get request options for the `fetch()` request\n*\n* @returns The request options\n* @internal\n*/\ngetRequestOptions_fn = function() {\n  var _a;\n  const init = {\n    // [spec] Let `corsAttributeState` be `Anonymous`…\n    // [spec] …will have their mode set to \"cors\"…\n    mode: \"cors\",\n    redirect: \"follow\",\n    headers: { Accept: \"text/event-stream\", ...__privateGet(this, _lastEventId) ? { \"Last-Event-ID\": __privateGet(this, _lastEventId) } : void 0 },\n    cache: \"no-store\",\n    signal: (_a = __privateGet(this, _controller)) == null ? void 0 : _a.signal\n  };\n  return \"window\" in globalThis && (init.credentials = this.withCredentials ? \"include\" : \"same-origin\"), init;\n}, _onEvent = /* @__PURE__ */ new WeakMap(), _onRetryChange = /* @__PURE__ */ new WeakMap(), /**\n* Handles the process referred to in the EventSource specification as \"failing a connection\".\n*\n* @param error - The error causing the connection to fail\n* @param code - The HTTP status code, if available\n* @internal\n*/\nfailConnection_fn = function(message, code) {\n  var _a;\n  __privateGet(this, _readyState) !== this.CLOSED && __privateSet(this, _readyState, this.CLOSED);\n  const errorEvent = new ErrorEvent(\"error\", { code, message });\n  (_a = __privateGet(this, _onError)) == null || _a.call(this, errorEvent), this.dispatchEvent(errorEvent);\n}, /**\n* Schedules a reconnection attempt against the EventSource endpoint.\n*\n* @param message - The error causing the connection to fail\n* @param code - The HTTP status code, if available\n* @internal\n*/\nscheduleReconnect_fn = function(message, code) {\n  var _a;\n  if (__privateGet(this, _readyState) === this.CLOSED)\n    return;\n  __privateSet(this, _readyState, this.CONNECTING);\n  const errorEvent = new ErrorEvent(\"error\", { code, message });\n  (_a = __privateGet(this, _onError)) == null || _a.call(this, errorEvent), this.dispatchEvent(errorEvent), __privateSet(this, _reconnectTimer, setTimeout(__privateGet(this, _reconnect), __privateGet(this, _reconnectInterval)));\n}, _reconnect = /* @__PURE__ */ new WeakMap(), /**\n* ReadyState representing an EventSource currently trying to connect\n*\n* @public\n*/\nEventSource.CONNECTING = 0, /**\n* ReadyState representing an EventSource connection that is open (eg connected)\n*\n* @public\n*/\nEventSource.OPEN = 1, /**\n* ReadyState representing an EventSource connection that is closed (eg disconnected)\n*\n* @public\n*/\nEventSource.CLOSED = 2;\nfunction getBaseURL() {\n  const doc = \"document\" in globalThis ? globalThis.document : void 0;\n  return doc && typeof doc == \"object\" && \"baseURI\" in doc && typeof doc.baseURI == \"string\" ? doc.baseURI : void 0;\n}\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/eventsource/dist/index.js\n");

/***/ })

};
;