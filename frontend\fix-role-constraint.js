import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const dbPath = path.join(__dirname, 'chat.db');
console.log('数据库路径:', dbPath);

try {
  const db = new Database(dbPath);
  
  console.log('开始修复role列的CHECK约束...');
  
  // 由于SQLite不支持直接修改CHECK约束，我们需要重建表
  // 1. 创建新表
  console.log('1. 创建新的messages表...');
  db.exec(`
    CREATE TABLE messages_new (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      conversation_id INTEGER NOT NULL,
      role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system', 'tool', 'tool_call', 'tool_result')),
      content TEXT NOT NULL,
      model TEXT,
      sequence_number INTEGER DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      timestamp INTEGER,
      -- 工具调用相关字段
      tool_name TEXT,
      tool_args TEXT,
      tool_result TEXT,
      tool_status TEXT CHECK (tool_status IN ('executing', 'completed', 'error')),
      tool_execution_time INTEGER,
      tool_error TEXT,
      -- Ollama生成统计信息
      total_duration INTEGER,
      load_duration INTEGER,
      prompt_eval_count INTEGER,
      prompt_eval_duration INTEGER,
      eval_count INTEGER,
      eval_duration INTEGER,
      FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE
    )
  `);
  
  // 2. 复制数据
  console.log('2. 复制现有数据...');
  db.exec(`
    INSERT INTO messages_new (
      id, conversation_id, role, content, model, sequence_number, created_at, timestamp,
      tool_name, tool_args, tool_result, tool_status, tool_execution_time, tool_error,
      total_duration, load_duration, prompt_eval_count, prompt_eval_duration, eval_count, eval_duration
    )
    SELECT 
      id, conversation_id, role, content, model, sequence_number, created_at, timestamp,
      tool_name, tool_args, tool_result, tool_status, tool_execution_time, tool_error,
      total_duration, load_duration, prompt_eval_count, prompt_eval_duration, eval_count, eval_duration
    FROM messages
  `);
  
  // 3. 删除旧表
  console.log('3. 删除旧表...');
  db.exec('DROP TABLE messages');
  
  // 4. 重命名新表
  console.log('4. 重命名新表...');
  db.exec('ALTER TABLE messages_new RENAME TO messages');
  
  // 5. 重建索引
  console.log('5. 重建索引...');
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages(conversation_id);
  `);
  
  // 6. 验证修复结果
  console.log('6. 验证修复结果...');
  
  // 测试插入tool_call类型的消息
  try {
    const testInsert = db.prepare(`
      INSERT INTO messages (conversation_id, role, content, model, timestamp)
      VALUES (1, 'tool_call', '测试工具调用', 'test', ?)
    `);
    const result = testInsert.run(Date.now());
    console.log('✅ tool_call角色插入成功，消息ID:', result.lastInsertRowid);
    
    // 删除测试消息
    db.prepare('DELETE FROM messages WHERE id = ?').run(result.lastInsertRowid);
    console.log('✅ 测试消息已清理');
    
  } catch (error) {
    console.error('❌ tool_call角色插入失败:', error);
  }
  
  // 检查表结构
  const columns = db.prepare(`PRAGMA table_info(messages)`).all();
  console.log('✅ 新表结构验证完成，列数:', columns.length);
  
  // 检查约束
  const schema = db.prepare(`SELECT sql FROM sqlite_master WHERE type='table' AND name='messages'`).get();
  console.log('✅ 新表约束:', schema.sql.includes("role IN ('user', 'assistant', 'system', 'tool', 'tool_call', 'tool_result')") ? '包含tool_call' : '不包含tool_call');
  
  console.log('\n🎉 role列CHECK约束修复完成！');
  console.log('现在可以正常创建tool_call类型的消息了。');
  
  db.close();
} catch (error) {
  console.error('修复失败:', error);
}
