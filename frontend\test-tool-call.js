import Database from 'better-sqlite3';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const dbPath = path.join(__dirname, 'chat.db');
console.log('数据库路径:', dbPath);

try {
  const db = new Database(dbPath);
  
  // 创建一个测试对话
  const createConversation = db.prepare(`
    INSERT INTO conversations (title, model, created_at, updated_at)
    VALUES (?, ?, datetime('now'), datetime('now'))
  `);
  
  const conversationResult = createConversation.run('工具调用测试', 'llama3.2:3b');
  const conversationId = conversationResult.lastInsertRowid;
  console.log('创建测试对话，ID:', conversationId);
  
  // 创建用户消息
  const createMessage = db.prepare(`
    INSERT INTO messages (conversation_id, role, content, model, timestamp)
    VALUES (?, ?, ?, ?, ?)
  `);
  
  const userMessageResult = createMessage.run(
    conversationId,
    'user',
    '现在几点了？',
    'llama3.2:3b',
    Date.now()
  );
  console.log('创建用户消息，ID:', userMessageResult.lastInsertRowid);
  
  // 创建工具调用消息
  const createToolCallMessage = db.prepare(`
    INSERT INTO messages (
      conversation_id, role, content, model, timestamp,
      tool_name, tool_args, tool_status, tool_execution_time
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
  `);
  
  const toolCallResult = createToolCallMessage.run(
    conversationId,
    'tool_call',
    '工具调用: get_current_time',
    'llama3.2:3b',
    Date.now() + 1000,
    'get_current_time',
    JSON.stringify({ timezone: 'Asia/Shanghai', format: 'YYYY-MM-DD HH:mm:ss' }),
    'executing',
    null
  );
  const toolCallMessageId = toolCallResult.lastInsertRowid;
  console.log('创建工具调用消息，ID:', toolCallMessageId);
  
  // 模拟工具执行完成，更新结果
  setTimeout(() => {
    const updateToolCall = db.prepare(`
      UPDATE messages SET
        tool_result = ?,
        tool_status = 'completed',
        tool_execution_time = ?
      WHERE id = ?
    `);
    
    const currentTime = new Date().toLocaleString('zh-CN', {
      timeZone: 'Asia/Shanghai',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
    
    updateToolCall.run(
      JSON.stringify({ time: currentTime, timezone: 'Asia/Shanghai' }),
      150, // 150ms执行时间
      toolCallMessageId
    );
    console.log('工具调用执行完成，结果已更新');
    
    // 创建助手回复消息
    const assistantMessageResult = createMessage.run(
      conversationId,
      'assistant',
      `根据工具调用结果，现在的时间是：${currentTime}（上海时区）`,
      'llama3.2:3b',
      Date.now() + 2000
    );
    console.log('创建助手回复消息，ID:', assistantMessageResult.lastInsertRowid);
    
    // 查询创建的消息
    const getMessages = db.prepare(`
      SELECT id, role, content, tool_name, tool_args, tool_result, tool_status, tool_execution_time
      FROM messages 
      WHERE conversation_id = ?
      ORDER BY timestamp ASC
    `);
    
    const messages = getMessages.all(conversationId);
    console.log('\n创建的测试消息:');
    messages.forEach(msg => {
      console.log(`ID: ${msg.id}, Role: ${msg.role}`);
      if (msg.role === 'tool_call') {
        console.log(`  工具: ${msg.tool_name}`);
        console.log(`  参数: ${msg.tool_args}`);
        console.log(`  状态: ${msg.tool_status}`);
        console.log(`  结果: ${msg.tool_result}`);
        console.log(`  执行时间: ${msg.tool_execution_time}ms`);
      } else {
        console.log(`  内容: ${msg.content}`);
      }
      console.log('---');
    });
    
    console.log(`\n测试完成！请在浏览器中访问对话 ID ${conversationId} 查看工具调用可视化效果`);
    console.log(`URL: http://localhost:3000/chat?conversation=${conversationId}`);
    
    db.close();
  }, 1000);
  
} catch (error) {
  console.error('测试失败:', error);
}
