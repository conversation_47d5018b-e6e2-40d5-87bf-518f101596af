import { db } from './connection';
import { mcpToolOperations } from './mcp-tools';
import type { McpTool<PERSON>all, CreateMcpToolCallData } from './types';

// MCP工具调用相关查询语句
export const mcpToolCallQueries = {
  // 创建工具调用记录
  create: db.prepare(`
    INSERT INTO mcp_tool_calls (
      tool_id, conversation_id, message_id, input_args, output_result,
      execution_time_ms, status, error_message
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
  `),

  // 获取对话的工具调用记录
  getByConversationId: db.prepare(`
    SELECT tc.*, t.name as tool_name, s.name as server_name
    FROM mcp_tool_calls tc
    JOIN mcp_tools t ON tc.tool_id = t.id
    JOIN mcp_servers s ON t.server_id = s.id
    WHERE tc.conversation_id = ?
    ORDER BY tc.created_at ASC
  `),

  // 获取工具的调用记录
  getByToolId: db.prepare(`
    SELECT * FROM mcp_tool_calls
    WHERE tool_id = ?
    ORDER BY created_at DESC
  `),

  // 获取最近的工具调用记录
  getRecent: db.prepare(`
    SELECT tc.*, t.name as tool_name, s.name as server_name
    FROM mcp_tool_calls tc
    JOIN mcp_tools t ON tc.tool_id = t.id
    JOIN mcp_servers s ON t.server_id = s.id
    ORDER BY tc.created_at DESC
    LIMIT ?
  `),

  // 获取工具调用统计信息
  getStats: db.prepare(`
    SELECT 
      COUNT(*) as total_calls,
      COUNT(CASE WHEN status = 'success' THEN 1 END) as success_calls,
      COUNT(CASE WHEN status = 'error' THEN 1 END) as error_calls,
      COUNT(CASE WHEN status = 'timeout' THEN 1 END) as timeout_calls,
      AVG(execution_time_ms) as avg_execution_time
    FROM mcp_tool_calls
    WHERE tool_id = ?
  `),

  // 删除对话的工具调用记录
  deleteByConversationId: db.prepare(`
    DELETE FROM mcp_tool_calls
    WHERE conversation_id = ?
  `),

  // 删除工具的调用记录
  deleteByToolId: db.prepare(`
    DELETE FROM mcp_tool_calls
    WHERE tool_id = ?
  `),
};

// MCP工具调用数据库操作函数
export const mcpToolCallOperations = {
  // 创建工具调用记录
  create(data: CreateMcpToolCallData): number {
    const result = mcpToolCallQueries.create.run(
      data.tool_id,
      data.conversation_id,
      data.message_id || null,
      data.input_args ? JSON.stringify(data.input_args) : null,
      data.output_result ? JSON.stringify(data.output_result) : null,
      data.execution_time_ms || null,
      data.status,
      data.error_message || null
    );
    
    // 更新工具使用统计
    if (data.status === 'success') {
      mcpToolOperations.updateUsage(data.tool_id);
    }
    
    return result.lastInsertRowid as number;
  },

  // 获取对话的工具调用记录
  getByConversationId(conversationId: number): any[] {
    return mcpToolCallQueries.getByConversationId.all(conversationId);
  },

  // 获取工具的调用记录
  getByToolId(toolId: number): McpToolCall[] {
    return mcpToolCallQueries.getByToolId.all(toolId) as McpToolCall[];
  },

  // 获取最近的工具调用记录
  getRecent(limit: number = 50): any[] {
    return mcpToolCallQueries.getRecent.all(limit);
  },

  // 获取工具调用统计信息
  getStats(toolId: number): any {
    return mcpToolCallQueries.getStats.get(toolId);
  },

  // 删除对话的工具调用记录
  deleteByConversationId(conversationId: number): void {
    mcpToolCallQueries.deleteByConversationId.run(conversationId);
  },

  // 删除工具的调用记录
  deleteByToolId(toolId: number): void {
    mcpToolCallQueries.deleteByToolId.run(toolId);
  },
};