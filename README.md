# Ollama Chat App

一个基于 Next.js、React、TypeScript 和 SQLite 的现代化 Ollama 聊天应用。

## 功能特性

- 🤖 **模型管理**: 自动获取本地 Ollama 模型列表
- 💬 **流式对话**: 支持实时流式输出的聊天体验
- 📚 **对话历史**: 使用 SQLite 数据库持久化存储聊天记录
- 🎨 **现代化 UI**: 基于 Tailwind CSS 的响应式设计
- 🌙 **深色模式**: 支持明暗主题切换
- ⚡ **高性能**: 使用 Next.js 14 和 App Router

## 技术栈

- **前端**: Next.js 14, React 18, TypeScript
- **样式**: Tailwind CSS
- **数据库**: SQLite (better-sqlite3)
- **API**: Ollama REST API
- **图标**: Lucide React

## 前置要求

1. **Node.js**: 版本 18 或更高
2. **Ollama**: 需要在本地安装并运行 Ollama
   - 下载地址: https://ollama.ai/
   - 确保 Ollama 在 `localhost:11434` 端口运行
   - 至少下载一个模型，例如: `ollama pull llama3.2`

## 安装和运行

### 1. 安装依赖

```bash
npm install
```

### 2. 启动开发服务器

```bash
npm run dev
```

### 3. 访问应用

打开浏览器访问 [http://localhost:3000](http://localhost:3000)

## 使用说明

### 首次使用

1. **确保 Ollama 运行**: 在终端运行 `ollama serve`
2. **下载模型**: 例如 `ollama pull llama3.2`
3. **启动应用**: 运行 `npm run dev`
4. **选择模型**: 在应用中点击设置图标选择要使用的模型
5. **开始聊天**: 点击"新建对话"开始与 AI 对话

### 主要功能

- **创建对话**: 点击"新建对话"按钮
- **选择模型**: 点击设置图标切换不同的 Ollama 模型
- **发送消息**: 在输入框中输入消息，按 Enter 发送
- **查看历史**: 左侧边栏显示所有历史对话
- **删除对话**: 点击对话旁的删除按钮

## 项目结构

```
src/
├── app/
│   ├── api/
│   │   ├── chat/          # 聊天 API 路由
│   │   ├── conversations/ # 对话管理 API
│   │   └── models/        # 模型列表 API
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页面
└── lib/
    ├── database.ts        # SQLite 数据库操作
    └── ollama.ts          # Ollama API 客户端
```

## API 端点

- `GET /api/models` - 获取可用模型列表
- `POST /api/chat` - 发送聊天消息（支持流式响应）
- `GET /api/conversations` - 获取所有对话
- `POST /api/conversations` - 创建新对话
- `GET /api/conversations/[id]` - 获取特定对话及消息
- `PATCH /api/conversations/[id]` - 更新对话标题
- `DELETE /api/conversations/[id]` - 删除对话

## 数据库结构

### conversations 表
- `id`: 主键
- `title`: 对话标题
- `model`: 使用的模型名称
- `created_at`: 创建时间
- `updated_at`: 更新时间

### messages 表
- `id`: 主键
- `conversation_id`: 对话 ID（外键）
- `role`: 消息角色（user/assistant/system）
- `content`: 消息内容
- `created_at`: 创建时间

## 开发指南

### 添加新功能

1. **API 路由**: 在 `src/app/api/` 目录下添加新的路由
2. **数据库操作**: 在 `src/lib/database.ts` 中添加新的查询函数
3. **前端组件**: 在主页面或创建新组件文件

### 环境配置

可以通过环境变量配置 Ollama 服务地址：

```bash
# .env.local
OLLAMA_BASE_URL=http://localhost:11434
```

## 故障排除

### 常见问题

1. **无法连接到 Ollama**
   - 确保 Ollama 正在运行: `ollama serve`
   - 检查端口是否为 11434
   - 确认防火墙设置

2. **没有可用模型**
   - 下载至少一个模型: `ollama pull llama3.2`
   - 检查模型是否正确安装: `ollama list`

3. **数据库错误**
   - 删除 `chat.db` 文件重新初始化数据库
   - 检查文件权限

4. **流式响应问题**
   - 检查网络连接
   - 确认 Ollama 版本支持流式 API

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License