import { NextRequest, NextResponse } from 'next/server';
import { ollamaClient, ChatMessage, Tool } from '../../../lib/ollama';
import { dbOperations, mcpDbOperations } from '../../../lib/database';
import { messageOperations } from '../../../lib/database/messages';
import { db } from '../../../lib/database/connection';
import { availableTools, ToolExecutor, testTool, getToolsByNames } from '../../../lib/tools';
import { mcpServerClient } from '../../../lib/mcp/mcp-client-server';

export async function POST(request: Request) {
  try {
    // 确保MCP服务器客户端已连接
    if (!mcpServerClient.isClientConnected()) {
      await mcpServerClient.connect();
    }
    
    const body = await request.json();
    const {
      model,
      messages,
      conversationId,
      stream = true,
      options = {},
      enableTools = true,
      testMode = false,
      selectedTools = []
    }: {
      model: string;
      messages: ChatMessage[];
      conversationId?: number;
      stream?: boolean;
      options?: any;
      enableTools?: boolean;
      testMode?: boolean;
      selectedTools?: string[];
    } = body;

    // 测试模式：仅验证模型是否支持工具，使用简单的测试工具
    if (testMode) {
      try {
        await ollamaClient.chat({
          model,
          messages: [{ role: 'user' as const, content: 'test' }],
          tools: enableTools ? [testTool] : undefined,
          stream: false,
        });
        return NextResponse.json({ success: true, supportsTools: true });
      } catch (error: any) {
        const errorMessage = error.message || '';
        if (errorMessage.includes('does not support tools')) {
          return NextResponse.json({ success: true, supportsTools: false });
        }
        return NextResponse.json({ success: false, error: errorMessage });
      }
    }

    // 验证必需参数
    if (!model || !messages || !Array.isArray(messages) || messages.length === 0) {
      return NextResponse.json(
        { error: '缺少必需参数: model 和 messages' },
        { status: 400 }
      );
    }

    // 检查Ollama服务是否可用
    const isAvailable = await ollamaClient.isAvailable();
    if (!isAvailable) {
      return NextResponse.json(
        { 
          error: 'Ollama服务不可用',
          message: '请确保Ollama正在运行并监听在localhost:11434端口'
        },
        { status: 503 }
      );
    }

    // 如果是流式响应
    if (stream) {
      const encoder = new TextEncoder();
      
      const readableStream = new ReadableStream({
        async start(controller) {
          try {
            let assistantMessage = '';
            let toolCallsToSave: any[] = [];
        let assistantStats: any = null;
            
            // 准备聊天请求，如果启用工具则添加用户选择的工具定义
            let userSelectedTools: Tool[] = [];
            
            if (enableTools && selectedTools.length > 0) {
              userSelectedTools = await getToolsByNames(selectedTools);
              console.log('用户选择的工具:', selectedTools);
              console.log('获取到的工具定义:', userSelectedTools);
            }
            
            let chatRequest = {
              model,
              messages,
              stream: true,
              options,
              ...(enableTools && userSelectedTools.length > 0 && { tools: userSelectedTools })
            };
            
            console.log('发送聊天请求:', JSON.stringify(chatRequest, null, 2));
            console.log('enableTools:', enableTools, 'selectedTools:', selectedTools, 'userSelectedTools:', userSelectedTools);
            
            let retryWithoutTools = false;
            
            try {
              // 立即保存用户消息，确保时间顺序正确
              if (conversationId) {
                const lastUserMessage = messages[messages.length - 1];
                if (lastUserMessage?.role === 'user') {
                  dbOperations.createMessage({
                    conversation_id: conversationId,
                    role: 'user' as const,
                    content: lastUserMessage.content,
                    model: model
                  });
                }
              }

              // 使用流式API
            for await (const chunk of ollamaClient.chatStream(chatRequest)) {
              // 处理工具调用
              if (chunk.message?.tool_calls && chunk.message.tool_calls.length > 0) {
                // 先保存AI决定调用工具前的回复内容（如果有）
                if (conversationId && assistantMessage.trim()) {
                  try {
                    dbOperations.createMessage({
                      conversation_id: conversationId,
                      role: 'assistant' as const,
                      content: assistantMessage,
                      model: model
                    });
                    // 重置助手消息内容，为工具调用后的回复做准备
                    assistantMessage = '';
                  } catch (dbError) {
                    console.error('保存AI工具调用前回复失败:', dbError);
                  }
                }

                // 执行工具调用
                for (const toolCall of chunk.message.tool_calls) {
                  const startTime = Date.now();
                  let toolResultMessageId: number | null = null;
                  let mcpToolCallId: number | null = null;
                  let currentToolCallMessageId: number | null = null;

                  try {
                    // 安全解析工具调用参数
                    let args = {};
                    if (toolCall.function.arguments) {
                      if (typeof toolCall.function.arguments === 'string') {
                        try {
                          args = JSON.parse(toolCall.function.arguments);
                        } catch (parseError) {
                          console.error('工具参数JSON解析失败:', parseError, '原始参数:', toolCall.function.arguments);
                          throw new Error(`工具参数格式错误: ${toolCall.function.arguments}`);
                        }
                      } else if (typeof toolCall.function.arguments === 'object') {
                        args = toolCall.function.arguments;
                      }
                    }
                    
                    // 发送工具调用开始状态
                    const toolStartData = `data: ${JSON.stringify({
                      type: 'tool_call_start',
                      tool_name: toolCall.function.name,
                      tool_args: args,
                      tool_call_id: toolCall.id
                    })}\n\n`;
                    controller.enqueue(encoder.encode(toolStartData));
                    
                    // 从用户选择的工具中查找对应的serverName
                    const selectedTool = userSelectedTools.find(tool => tool.function.name === toolCall.function.name);
                    const serverName = (selectedTool as any)?.serverName;
                    
                    // 立即创建工具调用消息
                    if (conversationId) {
                      try {
                        currentToolCallMessageId = messageOperations.createToolCall({
                          conversation_id: conversationId,
                          tool_name: toolCall.function.name,
                          tool_args: args,
                          tool_status: 'executing'
                        });
                      } catch (dbError) {
                        console.error('创建工具调用消息失败:', dbError);
                      }
                    }

                    console.log(`执行工具 ${toolCall.function.name}，使用服务器: ${serverName || '自动检测'}`);
                    const result = await ToolExecutor.executeToolCall(toolCall.function.name, args, serverName);
                    const executionTime = Date.now() - startTime;

                    // 更新工具调用消息状态
                    if (conversationId && currentToolCallMessageId) {
                      try {
                        const updateToolCall = db.prepare(`
                          UPDATE messages SET
                            tool_result = ?,
                            tool_status = 'completed',
                            tool_execution_time = ?
                          WHERE id = ?
                        `);
                        updateToolCall.run(
                          JSON.stringify(result),
                          executionTime,
                          currentToolCallMessageId
                        );
                      } catch (dbError) {
                        console.error('更新工具调用状态失败:', dbError);
                      }
                    }
                    
                    // 工具调用结果现在通过独立的ToolCallMessage组件显示，不再添加到助手消息中
                    
                    // 暂存工具调用信息，稍后关联到助手消息
                    if (conversationId) {
                      try {
                        const availableTools = mcpDbOperations.getAvailableMcpTools();
                        const mcpTool = availableTools.find(tool => tool.name === toolCall.function.name);
                        
                        if (mcpTool) {
                          // 将工具调用信息存储到临时数组中，等助手消息保存后再关联
                          if (!toolCallsToSave) {
                            toolCallsToSave = [];
                          }
                          toolCallsToSave.push({
                            tool_id: mcpTool.id,
                            input_args: args,
                            output_result: { result },
                            execution_time_ms: executionTime,
                            status: 'success' as const
                          });
                        }
                      } catch (dbError) {
                        console.error('准备MCP工具调用记录失败:', dbError);
                      }
                    }
                    
                    // 发送工具调用完成状态
                    const toolCompleteData = `data: ${JSON.stringify({
                      type: 'tool_call_complete',
                      tool_name: toolCall.function.name,
                      tool_args: args,
                      tool_result: result,
                      tool_call_id: toolCall.id,
                      execution_time: executionTime
                    })}\n\n`;
                    controller.enqueue(encoder.encode(toolCompleteData));
                    
                    // 工具调用结果已通过tool_call_complete事件发送，无需重复发送
                    
                    // 将工具结果添加到消息历史中，继续对话
                    const updatedMessages: ChatMessage[] = [
                      ...messages,
                      {
                        role: 'assistant' as const,
                        content: '',
                        tool_calls: [toolCall]
                      },
                      {
                        role: 'tool' as const,
                        content: result
                      }
                    ];
                    
                    // 继续对话以获取基于工具结果的回复
                    const followUpRequest = {
                      model,
                      messages: updatedMessages,
                      stream: true,
                      options
                    };
                    
                    let followUpMessage = '';
                    for await (const followUpChunk of ollamaClient.chatStream(followUpRequest)) {
                      if (followUpChunk.message?.content) {
                        followUpMessage += followUpChunk.message.content;
                      }
                      
                      const followUpData = `data: ${JSON.stringify(followUpChunk)}\n\n`;
                      controller.enqueue(encoder.encode(followUpData));
                      
                      if (followUpChunk.done && conversationId && followUpMessage.trim()) {
                        // 立即保存工具调用后的助手回复
                        const assistantMessageId = dbOperations.createMessage({
                          conversation_id: conversationId,
                          role: 'assistant' as const,
                          content: followUpMessage,
                          model: model,
                          total_duration: followUpChunk.total_duration,
                          load_duration: followUpChunk.load_duration,
                          prompt_eval_count: followUpChunk.prompt_eval_count,
                          prompt_eval_duration: followUpChunk.prompt_eval_duration,
                          eval_count: followUpChunk.eval_count,
                          eval_duration: followUpChunk.eval_duration
                        });

                        // 保存关联的工具调用记录
                        if (toolCallsToSave.length > 0) {
                          for (const toolCallData of toolCallsToSave) {
                            try {
                              mcpDbOperations.createMcpToolCall({
                                ...toolCallData,
                                conversation_id: conversationId,
                                message_id: assistantMessageId
                              });
                            } catch (dbError) {
                              console.error('保存MCP工具调用记录失败:', dbError);
                            }
                          }
                          toolCallsToSave = []; // 清空临时数组
                        }
                        break;
                      }
                    }
                  } catch (toolError) {
                    console.error('工具执行失败:', toolError);
                    const executionTime = Date.now() - startTime;
                    const errorMessage = toolError instanceof Error ? toolError.message : '未知错误';

                    // 更新工具调用消息状态为错误
                    if (conversationId && currentToolCallMessageId) {
                      try {
                        const updateToolCall = db.prepare(`
                          UPDATE messages SET
                            tool_status = 'error',
                            tool_execution_time = ?,
                            tool_error = ?
                          WHERE id = ?
                        `);
                        updateToolCall.run(
                          executionTime,
                          errorMessage,
                          currentToolCallMessageId
                        );
                      } catch (dbError) {
                        console.error('更新工具调用错误状态失败:', dbError);
                      }
                    }
                    
                    // 重新解析参数用于错误显示
                    let errorArgs = {};
                    try {
                      if (toolCall.function.arguments) {
                        errorArgs = typeof toolCall.function.arguments === 'string' 
                          ? JSON.parse(toolCall.function.arguments)
                          : toolCall.function.arguments;
                      }
                    } catch {
                      errorArgs = { raw: toolCall.function.arguments };
                    }
                    
                    // 发送工具调用错误状态
                    const toolErrorData = `data: ${JSON.stringify({
                      type: 'tool_call_error',
                      tool_name: toolCall.function.name,
                      tool_args: errorArgs,
                      error_message: errorMessage,
                      tool_call_id: toolCall.id,
                      execution_time: executionTime
                    })}\n\n`;
                    controller.enqueue(encoder.encode(toolErrorData));
                    
                    // 工具执行错误现在通过独立的ToolCallMessage组件显示，不再添加到助手消息中
                    
                    // 暂存失败的工具调用信息
                    if (conversationId) {
                      try {
                        const availableTools = mcpDbOperations.getAvailableMcpTools();
                        const mcpTool = availableTools.find(tool => tool.name === toolCall.function.name);
                        
                        if (mcpTool) {
                          // 安全解析参数，如果解析失败则使用原始字符串
                          let inputArgs = {};
                          try {
                             if (toolCall.function.arguments) {
                                inputArgs = typeof toolCall.function.arguments === 'string' 
                                  ? JSON.parse(toolCall.function.arguments)
                                  : toolCall.function.arguments;
                              }
                            } catch (parseError) {
                              console.error('解析工具参数失败，使用原始字符串:', parseError);
                              inputArgs = toolCall.function.arguments;
                            }
                            
                            // 将失败的工具调用信息存储到临时数组中
                            if (!toolCallsToSave) {
                              toolCallsToSave = [];
                            }
                            toolCallsToSave.push({
                              tool_id: mcpTool.id,
                              input_args: inputArgs,
                              output_result: undefined,
                              execution_time_ms: executionTime,
                              status: 'error' as const,
                              error_message: errorMessage
                            });
                          }
                        } catch (dbError) {
                          console.error('准备失败的MCP工具调用记录失败:', dbError);
                        }
                      }
                    
                    const errorResult = `工具执行失败: ${errorMessage}`;
                    const toolResultErrorData = `data: ${JSON.stringify({
                      message: {
                        role: 'tool' as const,
                        content: errorResult,
                        tool_call_id: toolCall.id
                      }
                    })}\n\n`;
                    controller.enqueue(encoder.encode(toolResultErrorData));
                  }
                }
              } else {
                // 累积助手的回复内容
                if (chunk.message?.content) {
                  assistantMessage += chunk.message.content;
                }
                
                // 发送数据块到客户端
                const data = `data: ${JSON.stringify(chunk)}\n\n`;
                controller.enqueue(encoder.encode(data));
                
                // 如果完成，立即保存助手回复
                if (chunk.done && conversationId && assistantMessage.trim()) {
                  try {
                    // 保存助手回复，包含统计信息
                    const assistantMessageId = dbOperations.createMessage({
                      conversation_id: conversationId,
                      role: 'assistant' as const,
                      content: assistantMessage,
                      model: model,
                      total_duration: chunk.total_duration,
                      load_duration: chunk.load_duration,
                      prompt_eval_count: chunk.prompt_eval_count,
                      prompt_eval_duration: chunk.prompt_eval_duration,
                      eval_count: chunk.eval_count,
                      eval_duration: chunk.eval_duration
                    });

                    // 保存关联的工具调用记录（如果有）
                    if (toolCallsToSave.length > 0) {
                      for (const toolCallData of toolCallsToSave) {
                        try {
                          mcpDbOperations.createMcpToolCall({
                            ...toolCallData,
                            conversation_id: conversationId,
                            message_id: assistantMessageId
                          });
                        } catch (dbError) {
                          console.error('保存MCP工具调用记录失败:', dbError);
                        }
                      }
                      toolCallsToSave = []; // 清空临时数组
                    }
                  } catch (dbError) {
                    console.error('保存助手消息到数据库失败:', dbError);
                  }
                }
              }
            }
            
            // 所有消息都已在各自的完成时点立即保存，无需额外处理

            // 发送结束标志
            controller.enqueue(encoder.encode('data: [DONE]\n\n'));
            controller.close();
          } catch (streamError) {
            console.error('流式请求错误:', streamError);
            
            // 检查是否是模型不支持工具的错误
            const errorMessage = streamError instanceof Error ? streamError.message : String(streamError);
            const isToolsNotSupported = errorMessage.includes('does not support tools');
            
            // 如果启用了工具且出现工具不支持错误，尝试不使用工具重新请求
            if (enableTools && !retryWithoutTools && isToolsNotSupported) {
              console.log('模型不支持工具调用，尝试不使用工具重新请求');
              retryWithoutTools = true;
              
              // 重置助手消息内容，避免重复累积
              assistantMessage = '';
              
              // 重新构建不包含工具的请求
              chatRequest = {
                model,
                messages,
                stream: true,
                options
              };
              
              // 重新尝试流式API
              for await (const chunk of ollamaClient.chatStream(chatRequest)) {
                // 累积助手的回复内容
                if (chunk.message?.content) {
                  assistantMessage += chunk.message.content;
                }
                
                // 发送数据块到客户端
                const data = `data: ${JSON.stringify(chunk)}\n\n`;
                controller.enqueue(encoder.encode(data));
                
                // 如果完成，保存助手消息到数据库（用户消息已在开始时保存）
                if (chunk.done && conversationId && assistantMessage.trim()) {
                  try {
                    // 保存助手回复
                    dbOperations.createMessage({
                      conversation_id: conversationId,
                      role: 'assistant' as const,
                      content: assistantMessage,
                      model: model
                    });
                  } catch (dbError) {
                    console.error('保存消息到数据库失败:', dbError);
                  }
                }
              }
              
              // 发送结束标志
              controller.enqueue(encoder.encode('data: [DONE]\n\n'));
              controller.close();
            } else {
              // 如果已经重试过或者没有启用工具，或者不是工具不支持的错误，直接抛出错误
              throw streamError;
            }
          }
          } catch (error) {
            console.error('流式聊天失败:', error);
            const errorData = `data: ${JSON.stringify({
              error: true,
              message: error instanceof Error ? error.message : '聊天请求失败'
            })}\n\n`;
            controller.enqueue(encoder.encode(errorData));
            controller.close();
          }
        }
      });

      return new Response(readableStream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    } else {
      // 非流式响应
      const userSelectedTools = enableTools && selectedTools.length > 0 
        ? await getToolsByNames(selectedTools) 
        : [];
      
      const chatRequest = {
        model,
        messages,
        stream: false,
        options,
        ...(enableTools && userSelectedTools.length > 0 && { tools: userSelectedTools })
      };
      
      console.log('非流式聊天请求:', JSON.stringify(chatRequest, null, 2));
      console.log('非流式 enableTools:', enableTools, 'selectedTools:', selectedTools, 'userSelectedTools:', userSelectedTools);
      
      let response = await ollamaClient.chat(chatRequest);
      let finalMessages: ChatMessage[] = [...messages];
      
      // 处理工具调用
      if (response.message?.tool_calls && response.message.tool_calls.length > 0) {
        for (const toolCall of response.message.tool_calls) {
          const startTime = Date.now();
          let toolCallMessageId: number | null = null;
          let toolResultMessageId: number | null = null;
          
          try {
            const args = JSON.parse(toolCall.function.arguments);
            
            // 保存工具调用消息到数据库
            if (conversationId) {
              try {
                toolCallMessageId = dbOperations.createMessage({
                  conversation_id: conversationId,
                  role: 'assistant' as const,
                  content: `调用工具: ${toolCall.function.name}\n参数: ${JSON.stringify(args, null, 2)}`,
                  model: model
                });
              } catch (dbError) {
                console.error('保存工具调用消息失败:', dbError);
              }
            }
            
            // 从用户选择的工具中查找对应的serverName
            const selectedTool = userSelectedTools.find(tool => tool.function.name === toolCall.function.name);
            const serverName = (selectedTool as any)?.serverName;
            
            console.log(`非流式执行工具 ${toolCall.function.name}，使用服务器: ${serverName || '自动检测'}`);
            const result = await ToolExecutor.executeToolCall(toolCall.function.name, args, serverName);
            const executionTime = Date.now() - startTime;
            
            // 保存工具结果消息到数据库
            if (conversationId) {
              try {
                toolResultMessageId = dbOperations.createMessage({
                  conversation_id: conversationId,
                  role: 'tool' as const,
                  content: result,
                  model: model
                });
              } catch (dbError) {
                console.error('保存工具结果消息失败:', dbError);
              }
            }
            
            // 保存MCP工具调用记录
            if (conversationId) {
              try {
                const availableTools = mcpDbOperations.getAvailableMcpTools();
                const mcpTool = availableTools.find(tool => tool.name === toolCall.function.name);
                
                if (mcpTool) {
                  // 工具调用记录将在助手消息保存时统一处理
                }
              } catch (dbError) {
                console.error('保存MCP工具调用记录失败:', dbError);
              }
            }
            
            // 添加工具调用和结果到消息历史
            finalMessages.push({
              role: 'assistant' as const,
              content: '',
              tool_calls: [toolCall]
            });
            
            finalMessages.push({
              role: 'tool' as const,
              content: result
            });
            
            // 继续对话以获取基于工具结果的回复
            const followUpResponse = await ollamaClient.chat({
              model,
              messages: finalMessages,
              stream: false,
              options
            });
            
            response = followUpResponse;
          } catch (toolError) {
            console.error('工具执行失败:', toolError);
            const errorMessage = toolError instanceof Error ? toolError.message : '未知错误';
            
            // 保存工具调用失败记录到数据库
            if (conversationId && response.message?.tool_calls?.[0]) {
              const toolCall = response.message.tool_calls[0];
              try {
                // 保存错误结果消息
                const toolResultMessageId = dbOperations.createMessage({
                  conversation_id: conversationId,
                  role: 'tool' as const,
                  content: `工具执行失败: ${errorMessage}`,
                  model: model
                });
                
                // 保存MCP工具调用记录
                const availableTools = mcpDbOperations.getAvailableMcpTools();
                const mcpTool = availableTools.find(tool => tool.name === toolCall.function.name);
                
                if (mcpTool) {
                  let inputArgs = {};
                  try {
                    if (toolCall.function.arguments) {
                      inputArgs = typeof toolCall.function.arguments === 'string'
                        ? JSON.parse(toolCall.function.arguments)
                        : toolCall.function.arguments;
                    }
                  } catch (parseError) {
                    inputArgs = { raw_arguments: toolCall.function.arguments };
                  }
                  
                  // 工具调用记录将在助手消息保存时统一处理
                }
              } catch (dbError) {
                console.error('保存工具调用失败记录失败:', dbError);
              }
            }
            
            // 在工具执行失败时，返回错误信息
            response.message.content = `工具执行失败: ${errorMessage}`;
          }
        }
      }

      // 保存消息到数据库
      if (conversationId) {
        try {
          // 保存用户消息
          const lastUserMessage = messages[messages.length - 1];
          if (lastUserMessage?.role === 'user') {
            dbOperations.createMessage({
              conversation_id: conversationId,
              role: 'user' as const,
              content: lastUserMessage.content,
              model: model
            });
          }
          
          // 保存助手回复
          if (response.message?.content) {
            dbOperations.createMessage({
              conversation_id: conversationId,
              role: 'assistant' as const,
              content: response.message.content,
              model: model
            });
          }
        } catch (dbError) {
          console.error('保存消息到数据库失败:', dbError);
        }
      }

      return NextResponse.json({
        success: true,
        response
      });
    }
  } catch (error) {
    console.error('ollama.ts chatStream 详细错误:', error);
    
    return NextResponse.json(
      { 
        error: '聊天请求失败',
        message: error instanceof Error ? `流式聊天请求失败: ${error.message}` : '流式聊天请求失败，请检查网络连接和Ollama服务状态'
      },
      { status: 500 }
    );
  }
}